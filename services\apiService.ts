
import {
  DeviceStatus,
  ColorSample,
  ScannedColorData,
  StandardCalibrationResponse
} from '../types';
import { DEVICE_BASE_URL } from '../constants';

const API_BASE_URL = DEVICE_BASE_URL; // Use ESP32 device IP address

async function handleResponse<T,>(response: Response): Promise<T> {
  // Check for "application/json" content type before parsing
  const contentType = response.headers.get("content-type");

  if (!response.ok) {
    let errorText: string;
    try {
      if (contentType && contentType.includes("application/json")) {
        const errorData = await response.json();
        errorText = errorData.error || errorData.message || JSON.stringify(errorData);
      } else {
        errorText = await response.text();
      }
    } catch {
      errorText = `${response.status} ${response.statusText}`;
    }
    throw new Error(`API Error: ${response.status} ${response.statusText} - ${errorText}`);
  }

  if (contentType && contentType.includes("application/json")) {
    return response.json() as Promise<T>;
  }
  // For text/plain responses, we might need to handle them differently or expect specific structures
  // For now, if not JSON, assume it's a simple text confirmation and wrap it if needed by the caller
  return response.text() as unknown as Promise<T>;
}


export async function getDeviceStatus(): Promise<DeviceStatus> {
  const response = await fetch(`${API_BASE_URL}/status`);
  return handleResponse<DeviceStatus>(response);
}

export async function startScan(): Promise<ScannedColorData> {
  const response = await fetch(`${API_BASE_URL}/scan`, { method: 'POST' });
  return handleResponse<ScannedColorData>(response);
}

export async function saveSample(r: number, g: number, b: number): Promise<string> {
  const response = await fetch(`${API_BASE_URL}/save`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ r, g, b }),
  });
  return handleResponse<string>(response); // Expects "Sample saved" or error
}

export async function getSavedSamples(): Promise<{ samples: ColorSample[] }> {
  const response = await fetch(`${API_BASE_URL}/samples`);
  return handleResponse<{ samples: ColorSample[] }>(response);
}

export async function deleteSample(index: number): Promise<{ success: boolean; message: string }> {
  const response = await fetch(`${API_BASE_URL}/delete`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ index }),
  });
  return handleResponse<{ success: boolean; message: string }>(response);
}

export async function clearAllSamples(): Promise<{ success: boolean; message: string }> {
  const response = await fetch(`${API_BASE_URL}/samples/clear`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
  });
  return handleResponse<{ success: boolean; message: string }>(response);
}

export async function updateSettings(settings: Partial<DeviceStatus>): Promise<string> {
  const response = await fetch(`${API_BASE_URL}/settings`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(settings),
  });
  return handleResponse<string>(response); // Expects "Settings saved"
}

export async function setLedBrightness(payload: {
  brightness: number;
  r?: number;
  g?: number;
  b?: number;
  keepOn?: boolean;
  enhancedMode?: boolean;
}): Promise<{success: boolean, brightness: number, actualBrightness: number, ledState: boolean, message: string}> {
  const response = await fetch(`${API_BASE_URL}/brightness`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(payload),
  });
  return handleResponse<{success: boolean, brightness: number, actualBrightness: number, ledState: boolean, message: string}>(response);
}


// Removed complex calibration API - using simple calibration only

// Standard Calibration API - Using actual ESP32 endpoint
export async function performWhiteCalibration(brightness: number = 128): Promise<{success: boolean, message: string}> {
  const response = await fetch(`${API_BASE_URL}/calibrate/white`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ brightness }),
  });
  return handleResponse<{success: boolean, message: string}>(response);
}

// Black point calibration - measures dark reference with LEDs OFF
export async function performBlackCalibration(): Promise<{success: boolean, message: string}> {
  try {
    const response = await fetch(`${DEVICE_BASE_URL}/calibrate/black`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return {
      success: result.success,
      message: result.message || 'Black calibration completed'
    };
  } catch (error) {
    console.error('Black calibration failed:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Black calibration failed'
    };
  }
}

export async function performVividWhiteCalibration(brightness: number = 128): Promise<{success: boolean, message: string}> {
  // Use the same white calibration endpoint - it's designed for Vivid White
  return performWhiteCalibration(brightness);
}

// Removed matrix calibration API - using simple calibration only
    