
export interface DeviceStatus {
  isScanning: boolean;
  ledState: boolean;
  isCalibrated: boolean;
  currentR: number;
  currentG: number;
  currentB: number;
  sampleCount: number;
  atime: number;
  again: number;
  brightness: number;
  ambientLux: number;
  // TCS3430 Advanced Calibration Settings
  autoZeroMode: number;
  autoZeroFreq: number;
  waitTime: number;
  esp32IP?: string;
  clientIP?: string;
  gateway?: string;
  subnet?: string;
  macAddress?: string;
  rssi?: number;
}

export interface ColorSample {
  r: number;
  g: number;
  b: number;
  timestamp: number;
  paintName: string;
  paintCode: string;
  lrv: number;
}

export interface ScannedColorData {
  r: number;
  g: number;
  b: number;
  x: number;
  y: number;
  z: number;
  ir: number;
}

// Removed complex calibration types - using simple calibration only

// Standard Calibration Types
export interface StandardCalibrationResponse {
  success: boolean;
  message: string;
}

// Removed matrix calibration types - using simple calibration only
    