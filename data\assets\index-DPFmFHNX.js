function e(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var t,n,r={exports:{}},a={};var l,s,i=(n||(n=1,r.exports=function(){if(t)return a;t=1;var e=Symbol.for("react.transitional.element"),n=Symbol.for("react.fragment");function r(t,n,r){var a=null;if(void 0!==r&&(a=""+r),void 0!==n.key&&(a=""+n.key),"key"in n)for(var l in r={},n)"key"!==l&&(r[l]=n[l]);else r=n;return n=r.ref,{$$typeof:e,type:t,key:a,ref:void 0!==n?n:null,props:r}}return a.Fragment=n,a.jsx=r,a.jsxs=r,a}()),r.exports),o={exports:{}},u={};function c(){if(l)return u;l=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.portal"),n=Symbol.for("react.fragment"),r=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),s=Symbol.for("react.consumer"),i=Symbol.for("react.context"),o=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),m=Symbol.iterator;var p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h=Object.assign,g={};function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}function v(){}function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}b.prototype.isReactComponent={},b.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},b.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=b.prototype;var x=y.prototype=new v;x.constructor=y,h(x,b.prototype),x.isPureReactComponent=!0;var w=Array.isArray,k={H:null,A:null,T:null,S:null,V:null},S=Object.prototype.hasOwnProperty;function N(t,n,r,a,l,s){return r=s.ref,{$$typeof:e,type:t,key:n,ref:void 0!==r?r:null,props:s}}function j(t){return"object"==typeof t&&null!==t&&t.$$typeof===e}var C=/\/+/g;function E(e,t){return"object"==typeof e&&null!==e&&null!=e.key?(n=""+e.key,r={"=":"=0",":":"=2"},"$"+n.replace(/[=:]/g,(function(e){return r[e]}))):t.toString(36);var n,r}function T(){}function P(n,r,a,l,s){var i=typeof n;"undefined"!==i&&"boolean"!==i||(n=null);var o,u,c=!1;if(null===n)c=!0;else switch(i){case"bigint":case"string":case"number":c=!0;break;case"object":switch(n.$$typeof){case e:case t:c=!0;break;case f:return P((c=n._init)(n._payload),r,a,l,s)}}if(c)return s=s(n),c=""===l?"."+E(n,0):l,w(s)?(a="",null!=c&&(a=c.replace(C,"$&/")+"/"),P(s,r,a,"",(function(e){return e}))):null!=s&&(j(s)&&(o=s,u=a+(null==s.key||n&&n.key===s.key?"":(""+s.key).replace(C,"$&/")+"/")+c,s=N(o.type,u,void 0,0,0,o.props)),r.push(s)),1;c=0;var d,p=""===l?".":l+":";if(w(n))for(var h=0;h<n.length;h++)c+=P(l=n[h],r,a,i=p+E(l,h),s);else if("function"==typeof(h=null===(d=n)||"object"!=typeof d?null:"function"==typeof(d=m&&d[m]||d["@@iterator"])?d:null))for(n=h.call(n),h=0;!(l=n.next()).done;)c+=P(l=l.value,r,a,i=p+E(l,h++),s);else if("object"===i){if("function"==typeof n.then)return P(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(T,T):(e.status="pending",e.then((function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)}),(function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)}))),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(n),r,a,l,s);throw r=String(n),Error("Objects are not valid as a React child (found: "+("[object Object]"===r?"object with keys {"+Object.keys(n).join(", ")+"}":r)+"). If you meant to render a collection of children, use an array instead.")}return c}function z(e,t,n){if(null==e)return e;var r=[],a=0;return P(e,r,"","",(function(e){return t.call(n,e,a++)})),r}function _(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var L="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e)};function O(){}return u.Children={map:z,forEach:function(e,t,n){z(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return z(e,(function(){t++})),t},toArray:function(e){return z(e,(function(e){return e}))||[]},only:function(e){if(!j(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},u.Component=b,u.Fragment=n,u.Profiler=a,u.PureComponent=y,u.StrictMode=r,u.Suspense=c,u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=k,u.__COMPILER_RUNTIME={__proto__:null,c:function(e){return k.H.useMemoCache(e)}},u.cache=function(e){return function(){return e.apply(null,arguments)}},u.cloneElement=function(e,t,n){if(null==e)throw Error("The argument must be a React element, but you passed "+e+".");var r=h({},e.props),a=e.key;if(null!=t)for(l in void 0!==t.ref&&void 0,void 0!==t.key&&(a=""+t.key),t)!S.call(t,l)||"key"===l||"__self"===l||"__source"===l||"ref"===l&&void 0===t.ref||(r[l]=t[l]);var l=arguments.length-2;if(1===l)r.children=n;else if(1<l){for(var s=Array(l),i=0;i<l;i++)s[i]=arguments[i+2];r.children=s}return N(e.type,a,void 0,0,0,r)},u.createContext=function(e){return(e={$$typeof:i,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:s,_context:e},e},u.createElement=function(e,t,n){var r,a={},l=null;if(null!=t)for(r in void 0!==t.key&&(l=""+t.key),t)S.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(a[r]=t[r]);var s=arguments.length-2;if(1===s)a.children=n;else if(1<s){for(var i=Array(s),o=0;o<s;o++)i[o]=arguments[o+2];a.children=i}if(e&&e.defaultProps)for(r in s=e.defaultProps)void 0===a[r]&&(a[r]=s[r]);return N(e,l,void 0,0,0,a)},u.createRef=function(){return{current:null}},u.forwardRef=function(e){return{$$typeof:o,render:e}},u.isValidElement=j,u.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:_}},u.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},u.startTransition=function(e){var t=k.T,n={};k.T=n;try{var r=e(),a=k.S;null!==a&&a(n,r),"object"==typeof r&&null!==r&&"function"==typeof r.then&&r.then(O,L)}catch(l){L(l)}finally{k.T=t}},u.unstable_useCacheRefresh=function(){return k.H.useCacheRefresh()},u.use=function(e){return k.H.use(e)},u.useActionState=function(e,t,n){return k.H.useActionState(e,t,n)},u.useCallback=function(e,t){return k.H.useCallback(e,t)},u.useContext=function(e){return k.H.useContext(e)},u.useDebugValue=function(){},u.useDeferredValue=function(e,t){return k.H.useDeferredValue(e,t)},u.useEffect=function(e,t,n){var r=k.H;if("function"==typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},u.useId=function(){return k.H.useId()},u.useImperativeHandle=function(e,t,n){return k.H.useImperativeHandle(e,t,n)},u.useInsertionEffect=function(e,t){return k.H.useInsertionEffect(e,t)},u.useLayoutEffect=function(e,t){return k.H.useLayoutEffect(e,t)},u.useMemo=function(e,t){return k.H.useMemo(e,t)},u.useOptimistic=function(e,t){return k.H.useOptimistic(e,t)},u.useReducer=function(e,t,n){return k.H.useReducer(e,t,n)},u.useRef=function(e){return k.H.useRef(e)},u.useState=function(e){return k.H.useState(e)},u.useSyncExternalStore=function(e,t,n){return k.H.useSyncExternalStore(e,t,n)},u.useTransition=function(){return k.H.useTransition()},u.version="19.1.0",u}function d(){return s||(s=1,o.exports=c()),o.exports}var f=d();const m=e(f);var p,h,g={exports:{}},b={},v={exports:{}},y={};function x(){return h||(h=1,v.exports=(p||(p=1,function(e){function t(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,l=e[r];if(!(0<a(l,t)))break e;e[r]=t,e[n]=l,n=r}}function n(e){return 0===e.length?null:e[0]}function r(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,l=e.length,s=l>>>1;r<s;){var i=2*(r+1)-1,o=e[i],u=i+1,c=e[u];if(0>a(o,n))u<l&&0>a(c,o)?(e[r]=c,e[u]=n,r=u):(e[r]=o,e[i]=n,r=i);else{if(!(u<l&&0>a(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(e.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var l=performance;e.unstable_now=function(){return l.now()}}else{var s=Date,i=s.now();e.unstable_now=function(){return s.now()-i}}var o=[],u=[],c=1,d=null,f=3,m=!1,p=!1,h=!1,g=!1,b="function"==typeof setTimeout?setTimeout:null,v="function"==typeof clearTimeout?clearTimeout:null,y="undefined"!=typeof setImmediate?setImmediate:null;function x(e){for(var a=n(u);null!==a;){if(null===a.callback)r(u);else{if(!(a.startTime<=e))break;r(u),a.sortIndex=a.expirationTime,t(o,a)}a=n(u)}}function w(e){if(h=!1,x(e),!p)if(null!==n(o))p=!0,S||(S=!0,k());else{var t=n(u);null!==t&&_(w,t.startTime-e)}}var k,S=!1,N=-1,j=5,C=-1;function E(){return!(!g&&e.unstable_now()-C<j)}function T(){if(g=!1,S){var t=e.unstable_now();C=t;var a=!0;try{e:{p=!1,h&&(h=!1,v(N),N=-1),m=!0;var l=f;try{t:{for(x(t),d=n(o);null!==d&&!(d.expirationTime>t&&E());){var s=d.callback;if("function"==typeof s){d.callback=null,f=d.priorityLevel;var i=s(d.expirationTime<=t);if(t=e.unstable_now(),"function"==typeof i){d.callback=i,x(t),a=!0;break t}d===n(o)&&r(o),x(t)}else r(o);d=n(o)}if(null!==d)a=!0;else{var c=n(u);null!==c&&_(w,c.startTime-t),a=!1}}break e}finally{d=null,f=l,m=!1}a=void 0}}finally{a?k():S=!1}}}if("function"==typeof y)k=function(){y(T)};else if("undefined"!=typeof MessageChannel){var P=new MessageChannel,z=P.port2;P.port1.onmessage=T,k=function(){z.postMessage(null)}}else k=function(){b(T,0)};function _(t,n){N=b((function(){t(e.unstable_now())}),n)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(e){e.callback=null},e.unstable_forceFrameRate=function(e){0>e||125<e||(j=0<e?Math.floor(1e3/e):5)},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_next=function(e){switch(f){case 1:case 2:case 3:var t=3;break;default:t=f}var n=f;f=t;try{return e()}finally{f=n}},e.unstable_requestPaint=function(){g=!0},e.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=f;f=e;try{return t()}finally{f=n}},e.unstable_scheduleCallback=function(r,a,l){var s=e.unstable_now();switch(l="object"==typeof l&&null!==l&&"number"==typeof(l=l.delay)&&0<l?s+l:s,r){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return r={id:c++,callback:a,priorityLevel:r,startTime:l,expirationTime:i=l+i,sortIndex:-1},l>s?(r.sortIndex=l,t(u,r),null===n(o)&&r===n(u)&&(h?(v(N),N=-1):h=!0,_(w,l-s))):(r.sortIndex=i,t(o,r),p||m||(p=!0,S||(S=!0,k()))),r},e.unstable_shouldYield=E,e.unstable_wrapCallback=function(e){var t=f;return function(){var n=f;f=t;try{return e.apply(this,arguments)}finally{f=n}}}}(y)),y)),v.exports}var w,k,S,N,j={exports:{}},C={};function E(){if(w)return C;w=1;var e=d();function t(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function n(){}var r={d:{f:n,r:function(){throw Error(t(522))},D:n,C:n,L:n,m:n,X:n,S:n,M:n},p:0,findDOMNode:null},a=Symbol.for("react.portal");var l=e.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function s(e,t){return"font"===e?"":"string"==typeof t?"use-credentials"===t?t:"":void 0}return C.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,C.createPortal=function(e,n){var r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!n||1!==n.nodeType&&9!==n.nodeType&&11!==n.nodeType)throw Error(t(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:a,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,n,null,r)},C.flushSync=function(e){var t=l.T,n=r.p;try{if(l.T=null,r.p=2,e)return e()}finally{l.T=t,r.p=n,r.d.f()}},C.preconnect=function(e,t){"string"==typeof e&&(t?t="string"==typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:t=null,r.d.C(e,t))},C.prefetchDNS=function(e){"string"==typeof e&&r.d.D(e)},C.preinit=function(e,t){if("string"==typeof e&&t&&"string"==typeof t.as){var n=t.as,a=s(n,t.crossOrigin),l="string"==typeof t.integrity?t.integrity:void 0,i="string"==typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?r.d.S(e,"string"==typeof t.precedence?t.precedence:void 0,{crossOrigin:a,integrity:l,fetchPriority:i}):"script"===n&&r.d.X(e,{crossOrigin:a,integrity:l,fetchPriority:i,nonce:"string"==typeof t.nonce?t.nonce:void 0})}},C.preinitModule=function(e,t){if("string"==typeof e)if("object"==typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=s(t.as,t.crossOrigin);r.d.M(e,{crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0})}}else null==t&&r.d.M(e)},C.preload=function(e,t){if("string"==typeof e&&"object"==typeof t&&null!==t&&"string"==typeof t.as){var n=t.as,a=s(n,t.crossOrigin);r.d.L(e,n,{crossOrigin:a,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0,type:"string"==typeof t.type?t.type:void 0,fetchPriority:"string"==typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"==typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"==typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"==typeof t.imageSizes?t.imageSizes:void 0,media:"string"==typeof t.media?t.media:void 0})}},C.preloadModule=function(e,t){if("string"==typeof e)if(t){var n=s(t.as,t.crossOrigin);r.d.m(e,{as:"string"==typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0})}else r.d.m(e)},C.requestFormReset=function(e){r.d.r(e)},C.unstable_batchedUpdates=function(e,t){return e(t)},C.useFormState=function(e,t,n){return l.H.useFormState(e,t,n)},C.useFormStatus=function(){return l.H.useHostTransitionStatus()},C.version="19.1.0",C}function T(){if(S)return b;S=1;var e=x(),t=d(),n=(k||(k=1,function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){}}(),j.exports=E()),j.exports);function r(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function a(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function l(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function s(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function i(e){if(l(e)!==e)throw Error(r(188))}function o(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=o(e)))return t;e=e.sibling}return null}var u=Object.assign,c=Symbol.for("react.element"),f=Symbol.for("react.transitional.element"),m=Symbol.for("react.portal"),p=Symbol.for("react.fragment"),h=Symbol.for("react.strict_mode"),g=Symbol.for("react.profiler"),v=Symbol.for("react.provider"),y=Symbol.for("react.consumer"),w=Symbol.for("react.context"),N=Symbol.for("react.forward_ref"),C=Symbol.for("react.suspense"),T=Symbol.for("react.suspense_list"),P=Symbol.for("react.memo"),z=Symbol.for("react.lazy"),_=Symbol.for("react.activity"),L=Symbol.for("react.memo_cache_sentinel"),O=Symbol.iterator;function M(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=O&&e[O]||e["@@iterator"])?e:null}var F=Symbol.for("react.client.reference");function R(e){if(null==e)return null;if("function"==typeof e)return e.$$typeof===F?null:e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case p:return"Fragment";case g:return"Profiler";case h:return"StrictMode";case C:return"Suspense";case T:return"SuspenseList";case _:return"Activity"}if("object"==typeof e)switch(e.$$typeof){case m:return"Portal";case w:return(e.displayName||"Context")+".Provider";case y:return(e._context.displayName||"Context")+".Consumer";case N:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case P:return null!==(t=e.displayName||null)?t:R(e.type)||"Memo";case z:t=e._payload,e=e._init;try{return R(e(t))}catch(n){}}return null}var D=Array.isArray,A=t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,I=n.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,$={pending:!1,data:null,method:null,action:null},U=[],V=-1;function B(e){return{current:e}}function H(e){0>V||(e.current=U[V],U[V]=null,V--)}function q(e,t){V++,U[V]=e.current,e.current=t}var W=B(null),Q=B(null),Z=B(null),Y=B(null);function G(e,t){switch(q(Z,t),q(Q,e),q(W,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?od(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=ud(t=od(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}H(W),q(W,e)}function K(){H(W),H(Q),H(Z)}function X(e){null!==e.memoizedState&&q(Y,e);var t=W.current,n=ud(t,e.type);t!==n&&(q(Q,e),q(W,n))}function J(e){Q.current===e&&(H(W),H(Q)),Y.current===e&&(H(Y),Xd._currentValue=$)}var ee=Object.prototype.hasOwnProperty,te=e.unstable_scheduleCallback,ne=e.unstable_cancelCallback,re=e.unstable_shouldYield,ae=e.unstable_requestPaint,le=e.unstable_now,se=e.unstable_getCurrentPriorityLevel,ie=e.unstable_ImmediatePriority,oe=e.unstable_UserBlockingPriority,ue=e.unstable_NormalPriority,ce=e.unstable_LowPriority,de=e.unstable_IdlePriority,fe=e.log,me=e.unstable_setDisableYieldValue,pe=null,he=null;function ge(e){if("function"==typeof fe&&me(e),he&&"function"==typeof he.setStrictMode)try{he.setStrictMode(pe,e)}catch(t){}}var be=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(ve(e)/ye|0)|0},ve=Math.log,ye=Math.LN2;var xe=256,we=4194304;function ke(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Se(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var a=0,l=e.suspendedLanes,s=e.pingedLanes;e=e.warmLanes;var i=134217727&r;return 0!==i?0!==(r=i&~l)?a=ke(r):0!==(s&=i)?a=ke(s):n||0!==(n=i&~e)&&(a=ke(n)):0!==(i=r&~l)?a=ke(i):0!==s?a=ke(s):n||0!==(n=r&~e)&&(a=ke(n)),0===a?0:0!==t&&t!==a&&0===(t&l)&&((l=a&-a)>=(n=t&-t)||32===l&&4194048&n)?t:a}function Ne(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function je(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function Ce(){var e=xe;return!(4194048&(xe<<=1))&&(xe=256),e}function Ee(){var e=we;return!(62914560&(we<<=1))&&(we=4194304),e}function Te(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Pe(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function ze(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-be(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function _e(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-be(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}function Le(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Oe(e){return 2<(e&=-e)?8<e?134217727&e?32:268435456:8:2}function Me(){var e=I.p;return 0!==e?e:void 0===(e=window.event)?32:pf(e.type)}var Fe=Math.random().toString(36).slice(2),Re="__reactFiber$"+Fe,De="__reactProps$"+Fe,Ae="__reactContainer$"+Fe,Ie="__reactEvents$"+Fe,$e="__reactListeners$"+Fe,Ue="__reactHandles$"+Fe,Ve="__reactResources$"+Fe,Be="__reactMarker$"+Fe;function He(e){delete e[Re],delete e[De],delete e[Ie],delete e[$e],delete e[Ue]}function qe(e){var t=e[Re];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ae]||n[Re]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=Sd(e);null!==e;){if(n=e[Re])return n;e=Sd(e)}return t}n=(e=n).parentNode}return null}function We(e){if(e=e[Re]||e[Ae]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function Qe(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(r(33))}function Ze(e){var t=e[Ve];return t||(t=e[Ve]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ye(e){e[Be]=!0}var Ge=new Set,Ke={};function Xe(e,t){Je(e,t),Je(e+"Capture",t)}function Je(e,t){for(Ke[e]=t,e=0;e<t.length;e++)Ge.add(t[e])}var et,tt,nt=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),rt={},at={};function lt(e,t,n){if(a=t,ee.call(at,a)||!ee.call(rt,a)&&(nt.test(a)?at[a]=!0:(rt[a]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var a}function st(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function it(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function ot(e){if(void 0===et)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);et=t&&t[1]||"",tt=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+et+e+tt}var ut=!1;function ct(e,t){if(!e||ut)return"";ut=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(a){var r=a}Reflect.construct(e,[],n)}else{try{n.call()}catch(l){r=l}e.call(n.prototype)}}else{try{throw Error()}catch(s){r=s}(n=e())&&"function"==typeof n.catch&&n.catch((function(){}))}}catch(i){if(i&&r&&"string"==typeof i.stack)return[i.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var l=r.DetermineComponentFrameRoot(),s=l[0],i=l[1];if(s&&i){var o=s.split("\n"),u=i.split("\n");for(a=r=0;r<o.length&&!o[r].includes("DetermineComponentFrameRoot");)r++;for(;a<u.length&&!u[a].includes("DetermineComponentFrameRoot");)a++;if(r===o.length||a===u.length)for(r=o.length-1,a=u.length-1;1<=r&&0<=a&&o[r]!==u[a];)a--;for(;1<=r&&0<=a;r--,a--)if(o[r]!==u[a]){if(1!==r||1!==a)do{if(r--,0>--a||o[r]!==u[a]){var c="\n"+o[r].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}}while(1<=r&&0<=a);break}}}finally{ut=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?ot(n):""}function dt(e){switch(e.tag){case 26:case 27:case 5:return ot(e.type);case 16:return ot("Lazy");case 13:return ot("Suspense");case 19:return ot("SuspenseList");case 0:case 15:return ct(e.type,!1);case 11:return ct(e.type.render,!1);case 1:return ct(e.type,!0);case 31:return ot("Activity");default:return""}}function ft(e){try{var t="";do{t+=dt(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function mt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function pt(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function ht(e){e._valueTracker||(e._valueTracker=function(e){var t=pt(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var a=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function gt(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=pt(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function bt(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var vt=/[\n"\\]/g;function yt(e){return e.replace(vt,(function(e){return"\\"+e.charCodeAt(0).toString(16)+" "}))}function xt(e,t,n,r,a,l,s,i){e.name="",null!=s&&"function"!=typeof s&&"symbol"!=typeof s&&"boolean"!=typeof s?e.type=s:e.removeAttribute("type"),null!=t?"number"===s?(0===t&&""===e.value||e.value!=t)&&(e.value=""+mt(t)):e.value!==""+mt(t)&&(e.value=""+mt(t)):"submit"!==s&&"reset"!==s||e.removeAttribute("value"),null!=t?kt(e,s,mt(t)):null!=n?kt(e,s,mt(n)):null!=r&&e.removeAttribute("value"),null==a&&null!=l&&(e.defaultChecked=!!l),null!=a&&(e.checked=a&&"function"!=typeof a&&"symbol"!=typeof a),null!=i&&"function"!=typeof i&&"symbol"!=typeof i&&"boolean"!=typeof i?e.name=""+mt(i):e.removeAttribute("name")}function wt(e,t,n,r,a,l,s,i){if(null!=l&&"function"!=typeof l&&"symbol"!=typeof l&&"boolean"!=typeof l&&(e.type=l),null!=t||null!=n){if(("submit"===l||"reset"===l)&&null==t)return;n=null!=n?""+mt(n):"",t=null!=t?""+mt(t):n,i||t===e.value||(e.value=t),e.defaultValue=t}r="function"!=typeof(r=null!=r?r:a)&&"symbol"!=typeof r&&!!r,e.checked=i?e.checked:!!r,e.defaultChecked=!!r,null!=s&&"function"!=typeof s&&"symbol"!=typeof s&&"boolean"!=typeof s&&(e.name=s)}function kt(e,t,n){"number"===t&&bt(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function St(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+mt(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function Nt(e,t,n){null==t||((t=""+mt(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+mt(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function jt(e,t,n,a){if(null==t){if(null!=a){if(null!=n)throw Error(r(92));if(D(a)){if(1<a.length)throw Error(r(93));a=a[0]}n=a}null==n&&(n=""),t=n}n=mt(t),e.defaultValue=n,(a=e.textContent)===n&&""!==a&&null!==a&&(e.value=a)}function Ct(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var Et=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Tt(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"==typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!=typeof n||0===n||Et.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Pt(e,t,n){if(null!=t&&"object"!=typeof t)throw Error(r(62));if(e=e.style,null!=n){for(var a in n)!n.hasOwnProperty(a)||null!=t&&t.hasOwnProperty(a)||(0===a.indexOf("--")?e.setProperty(a,""):"float"===a?e.cssFloat="":e[a]="");for(var l in t)a=t[l],t.hasOwnProperty(l)&&n[l]!==a&&Tt(e,l,a)}else for(var s in t)t.hasOwnProperty(s)&&Tt(e,s,t[s])}function zt(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var _t=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Lt=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Ot(e){return Lt.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Mt=null;function Ft(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Rt=null,Dt=null;function At(e){var t=We(e);if(t&&(e=t.stateNode)){var n=e[De]||null;e:switch(e=t.stateNode,t.type){case"input":if(xt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+yt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var a=n[t];if(a!==e&&a.form===e.form){var l=a[De]||null;if(!l)throw Error(r(90));xt(a,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name)}}for(t=0;t<n.length;t++)(a=n[t]).form===e.form&&gt(a)}break e;case"textarea":Nt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&St(e,!!n.multiple,t,!1)}}}var It=!1;function $t(e,t,n){if(It)return e(t,n);It=!0;try{return e(t)}finally{if(It=!1,(null!==Rt||null!==Dt)&&(qu(),Rt&&(t=Rt,e=Dt,Dt=Rt=null,At(t),e)))for(t=0;t<e.length;t++)At(e[t])}}function Ut(e,t){var n=e.stateNode;if(null===n)return null;var a=n[De]||null;if(null===a)return null;n=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(a=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!a;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(r(231,t,typeof n));return n}var Vt=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),Bt=!1;if(Vt)try{var Ht={};Object.defineProperty(Ht,"passive",{get:function(){Bt=!0}}),window.addEventListener("test",Ht,Ht),window.removeEventListener("test",Ht,Ht)}catch(Af){Bt=!1}var qt=null,Wt=null,Qt=null;function Zt(){if(Qt)return Qt;var e,t,n=Wt,r=n.length,a="value"in qt?qt.value:qt.textContent,l=a.length;for(e=0;e<r&&n[e]===a[e];e++);var s=r-e;for(t=1;t<=s&&n[r-t]===a[l-t];t++);return Qt=a.slice(e,1<t?1-t:void 0)}function Yt(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function Gt(){return!0}function Kt(){return!1}function Xt(e){function t(t,n,r,a,l){for(var s in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(s)&&(t=e[s],this[s]=t?t(a):a[s]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?Gt:Kt,this.isPropagationStopped=Kt,this}return u(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Gt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Gt)},persist:function(){},isPersistent:Gt}),t}var Jt,en,tn,nn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},rn=Xt(nn),an=u({},nn,{view:0,detail:0}),ln=Xt(an),sn=u({},an,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:vn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==tn&&(tn&&"mousemove"===e.type?(Jt=e.screenX-tn.screenX,en=e.screenY-tn.screenY):en=Jt=0,tn=e),Jt)},movementY:function(e){return"movementY"in e?e.movementY:en}}),on=Xt(sn),un=Xt(u({},sn,{dataTransfer:0})),cn=Xt(u({},an,{relatedTarget:0})),dn=Xt(u({},nn,{animationName:0,elapsedTime:0,pseudoElement:0})),fn=Xt(u({},nn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),mn=Xt(u({},nn,{data:0})),pn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},hn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},gn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function bn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=gn[e])&&!!t[e]}function vn(){return bn}var yn=Xt(u({},an,{key:function(e){if(e.key){var t=pn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Yt(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?hn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:vn,charCode:function(e){return"keypress"===e.type?Yt(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Yt(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),xn=Xt(u({},sn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),wn=Xt(u({},an,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:vn})),kn=Xt(u({},nn,{propertyName:0,elapsedTime:0,pseudoElement:0})),Sn=Xt(u({},sn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),Nn=Xt(u({},nn,{newState:0,oldState:0})),jn=[9,13,27,32],Cn=Vt&&"CompositionEvent"in window,En=null;Vt&&"documentMode"in document&&(En=document.documentMode);var Tn=Vt&&"TextEvent"in window&&!En,Pn=Vt&&(!Cn||En&&8<En&&11>=En),zn=String.fromCharCode(32),_n=!1;function Ln(e,t){switch(e){case"keyup":return-1!==jn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function On(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var Mn=!1;var Fn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Rn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Fn[e.type]:"textarea"===t}function Dn(e,t,n,r){Rt?Dt?Dt.push(r):Dt=[r]:Rt=r,0<(t=Zc(t,"onChange")).length&&(n=new rn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var An=null,In=null;function $n(e){$c(e,0)}function Un(e){if(gt(Qe(e)))return e}function Vn(e,t){if("change"===e)return t}var Bn=!1;if(Vt){var Hn;if(Vt){var qn="oninput"in document;if(!qn){var Wn=document.createElement("div");Wn.setAttribute("oninput","return;"),qn="function"==typeof Wn.oninput}Hn=qn}else Hn=!1;Bn=Hn&&(!document.documentMode||9<document.documentMode)}function Qn(){An&&(An.detachEvent("onpropertychange",Zn),In=An=null)}function Zn(e){if("value"===e.propertyName&&Un(In)){var t=[];Dn(t,In,e,Ft(e)),$t($n,t)}}function Yn(e,t,n){"focusin"===e?(Qn(),In=n,(An=t).attachEvent("onpropertychange",Zn)):"focusout"===e&&Qn()}function Gn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Un(In)}function Kn(e,t){if("click"===e)return Un(t)}function Xn(e,t){if("input"===e||"change"===e)return Un(t)}var Jn="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function er(e,t){if(Jn(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!ee.call(t,a)||!Jn(e[a],t[a]))return!1}return!0}function tr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function nr(e,t){var n,r=tr(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=tr(r)}}function rr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?rr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function ar(e){for(var t=bt((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=bt((e=t.contentWindow).document)}return t}function lr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var sr=Vt&&"documentMode"in document&&11>=document.documentMode,ir=null,or=null,ur=null,cr=!1;function dr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;cr||null==ir||ir!==bt(r)||("selectionStart"in(r=ir)&&lr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},ur&&er(ur,r)||(ur=r,0<(r=Zc(or,"onSelect")).length&&(t=new rn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=ir)))}function fr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var mr={animationend:fr("Animation","AnimationEnd"),animationiteration:fr("Animation","AnimationIteration"),animationstart:fr("Animation","AnimationStart"),transitionrun:fr("Transition","TransitionRun"),transitionstart:fr("Transition","TransitionStart"),transitioncancel:fr("Transition","TransitionCancel"),transitionend:fr("Transition","TransitionEnd")},pr={},hr={};function gr(e){if(pr[e])return pr[e];if(!mr[e])return e;var t,n=mr[e];for(t in n)if(n.hasOwnProperty(t)&&t in hr)return pr[e]=n[t];return e}Vt&&(hr=document.createElement("div").style,"AnimationEvent"in window||(delete mr.animationend.animation,delete mr.animationiteration.animation,delete mr.animationstart.animation),"TransitionEvent"in window||delete mr.transitionend.transition);var br=gr("animationend"),vr=gr("animationiteration"),yr=gr("animationstart"),xr=gr("transitionrun"),wr=gr("transitionstart"),kr=gr("transitioncancel"),Sr=gr("transitionend"),Nr=new Map,jr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Cr(e,t){Nr.set(e,t),Xe(t,[e])}jr.push("scrollEnd");var Er=new WeakMap;function Tr(e,t){if("object"==typeof e&&null!==e){var n=Er.get(e);return void 0!==n?n:(t={value:e,source:t,stack:ft(t)},Er.set(e,t),t)}return{value:e,source:t,stack:ft(t)}}var Pr=[],zr=0,_r=0;function Lr(){for(var e=zr,t=_r=zr=0;t<e;){var n=Pr[t];Pr[t++]=null;var r=Pr[t];Pr[t++]=null;var a=Pr[t];Pr[t++]=null;var l=Pr[t];if(Pr[t++]=null,null!==r&&null!==a){var s=r.pending;null===s?a.next=a:(a.next=s.next,s.next=a),r.pending=a}0!==l&&Rr(n,a,l)}}function Or(e,t,n,r){Pr[zr++]=e,Pr[zr++]=t,Pr[zr++]=n,Pr[zr++]=r,_r|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function Mr(e,t,n,r){return Or(e,t,n,r),Dr(e)}function Fr(e,t){return Or(e,null,null,t),Dr(e)}function Rr(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var a=!1,l=e.return;null!==l;)l.childLanes|=n,null!==(r=l.alternate)&&(r.childLanes|=n),22===l.tag&&(null===(e=l.stateNode)||1&e._visibility||(a=!0)),e=l,l=l.return;return 3===e.tag?(l=e.stateNode,a&&null!==t&&(a=31-be(n),null===(r=(e=l.hiddenUpdates)[a])?e[a]=[t]:r.push(t),t.lane=536870912|n),l):null}function Dr(e){if(50<Ru)throw Ru=0,Du=null,Error(r(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Ar={};function Ir(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function $r(e,t,n,r){return new Ir(e,t,n,r)}function Ur(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Vr(e,t){var n=e.alternate;return null===n?((n=$r(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Br(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Hr(e,t,n,a,l,s){var i=0;if(a=e,"function"==typeof e)Ur(e)&&(i=1);else if("string"==typeof e)i=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!=typeof t.precedence||"string"!=typeof t.href||""===t.href)break;return!0;case"link":if("string"!=typeof t.rel||"string"!=typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"==typeof t.precedence&&null==e);case"script":if(t.async&&"function"!=typeof t.async&&"symbol"!=typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"==typeof t.src)return!0}return!1}(e,n,W.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case _:return(e=$r(31,n,t,l)).elementType=_,e.lanes=s,e;case p:return qr(n.children,l,s,t);case h:i=8,l|=24;break;case g:return(e=$r(12,n,t,2|l)).elementType=g,e.lanes=s,e;case C:return(e=$r(13,n,t,l)).elementType=C,e.lanes=s,e;case T:return(e=$r(19,n,t,l)).elementType=T,e.lanes=s,e;default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case v:case w:i=10;break e;case y:i=9;break e;case N:i=11;break e;case P:i=14;break e;case z:i=16,a=null;break e}i=29,n=Error(r(130,null===e?"null":typeof e,"")),a=null}return(t=$r(i,n,t,l)).elementType=e,t.type=a,t.lanes=s,t}function qr(e,t,n,r){return(e=$r(7,e,r,t)).lanes=n,e}function Wr(e,t,n){return(e=$r(6,e,null,t)).lanes=n,e}function Qr(e,t,n){return(t=$r(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Zr=[],Yr=0,Gr=null,Kr=0,Xr=[],Jr=0,ea=null,ta=1,na="";function ra(e,t){Zr[Yr++]=Kr,Zr[Yr++]=Gr,Gr=e,Kr=t}function aa(e,t,n){Xr[Jr++]=ta,Xr[Jr++]=na,Xr[Jr++]=ea,ea=e;var r=ta;e=na;var a=32-be(r)-1;r&=~(1<<a),n+=1;var l=32-be(t)+a;if(30<l){var s=a-a%5;l=(r&(1<<s)-1).toString(32),r>>=s,a-=s,ta=1<<32-be(t)+a|n<<a|r,na=l+e}else ta=1<<l|n<<a|r,na=e}function la(e){null!==e.return&&(ra(e,1),aa(e,1,0))}function sa(e){for(;e===Gr;)Gr=Zr[--Yr],Zr[Yr]=null,Kr=Zr[--Yr],Zr[Yr]=null;for(;e===ea;)ea=Xr[--Jr],Xr[Jr]=null,na=Xr[--Jr],Xr[Jr]=null,ta=Xr[--Jr],Xr[Jr]=null}var ia=null,oa=null,ua=!1,ca=null,da=!1,fa=Error(r(519));function ma(e){throw ya(Tr(Error(r(418,"")),e)),fa}function pa(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[Re]=e,t[De]=r,n){case"dialog":Uc("cancel",t),Uc("close",t);break;case"iframe":case"object":case"embed":Uc("load",t);break;case"video":case"audio":for(n=0;n<Ac.length;n++)Uc(Ac[n],t);break;case"source":Uc("error",t);break;case"img":case"image":case"link":Uc("error",t),Uc("load",t);break;case"details":Uc("toggle",t);break;case"input":Uc("invalid",t),wt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),ht(t);break;case"select":Uc("invalid",t);break;case"textarea":Uc("invalid",t),jt(t,r.value,r.defaultValue,r.children),ht(t)}"string"!=typeof(n=r.children)&&"number"!=typeof n&&"bigint"!=typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||ed(t.textContent,n)?(null!=r.popover&&(Uc("beforetoggle",t),Uc("toggle",t)),null!=r.onScroll&&Uc("scroll",t),null!=r.onScrollEnd&&Uc("scrollend",t),null!=r.onClick&&(t.onclick=td),t=!0):t=!1,t||ma(e)}function ha(e){for(ia=e.return;ia;)switch(ia.tag){case 5:case 13:return void(da=!1);case 27:case 3:return void(da=!0);default:ia=ia.return}}function ga(e){if(e!==ia)return!1;if(!ua)return ha(e),ua=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||cd(e.type,e.memoizedProps)),t=!t),t&&oa&&ma(e),ha(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(r(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){oa=wd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}oa=null}}else 27===n?(n=oa,bd(e.type)?(e=kd,kd=null,oa=e):oa=n):oa=ia?wd(e.stateNode.nextSibling):null;return!0}function ba(){oa=ia=null,ua=!1}function va(){var e=ca;return null!==e&&(null===Su?Su=e:Su.push.apply(Su,e),ca=null),e}function ya(e){null===ca?ca=[e]:ca.push(e)}var xa=B(null),wa=null,ka=null;function Sa(e,t,n){q(xa,t._currentValue),t._currentValue=n}function Na(e){e._currentValue=xa.current,H(xa)}function ja(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ca(e,t,n,a){var l=e.child;for(null!==l&&(l.return=e);null!==l;){var s=l.dependencies;if(null!==s){var i=l.child;s=s.firstContext;e:for(;null!==s;){var o=s;s=l;for(var u=0;u<t.length;u++)if(o.context===t[u]){s.lanes|=n,null!==(o=s.alternate)&&(o.lanes|=n),ja(s.return,n,e),a||(i=null);break e}s=o.next}}else if(18===l.tag){if(null===(i=l.return))throw Error(r(341));i.lanes|=n,null!==(s=i.alternate)&&(s.lanes|=n),ja(i,n,e),i=null}else i=l.child;if(null!==i)i.return=l;else for(i=l;null!==i;){if(i===e){i=null;break}if(null!==(l=i.sibling)){l.return=i.return,i=l;break}i=i.return}l=i}}function Ea(e,t,n,a){e=null;for(var l=t,s=!1;null!==l;){if(!s)if(524288&l.flags)s=!0;else if(262144&l.flags)break;if(10===l.tag){var i=l.alternate;if(null===i)throw Error(r(387));if(null!==(i=i.memoizedProps)){var o=l.type;Jn(l.pendingProps.value,i.value)||(null!==e?e.push(o):e=[o])}}else if(l===Y.current){if(null===(i=l.alternate))throw Error(r(387));i.memoizedState.memoizedState!==l.memoizedState.memoizedState&&(null!==e?e.push(Xd):e=[Xd])}l=l.return}null!==e&&Ca(t,e,n,a),t.flags|=262144}function Ta(e){for(e=e.firstContext;null!==e;){if(!Jn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Pa(e){wa=e,ka=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function za(e){return La(wa,e)}function _a(e,t){return null===wa&&Pa(e),La(e,t)}function La(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===ka){if(null===e)throw Error(r(308));ka=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else ka=ka.next=t;return n}var Oa="undefined"!=typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach((function(e){return e()}))}},Ma=e.unstable_scheduleCallback,Fa=e.unstable_NormalPriority,Ra={$$typeof:w,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Da(){return{controller:new Oa,data:new Map,refCount:0}}function Aa(e){e.refCount--,0===e.refCount&&Ma(Fa,(function(){e.controller.abort()}))}var Ia=null,$a=0,Ua=0,Va=null;function Ba(){if(0===--$a&&null!==Ia){null!==Va&&(Va.status="fulfilled");var e=Ia;Ia=null,Ua=0,Va=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Ha=A.S;A.S=function(e,t){"object"==typeof t&&null!==t&&"function"==typeof t.then&&function(e,t){if(null===Ia){var n=Ia=[];$a=0,Ua=Oc(),Va={status:"pending",value:void 0,then:function(e){n.push(e)}}}$a++,t.then(Ba,Ba)}(0,t),null!==Ha&&Ha(e,t)};var qa=B(null);function Wa(){var e=qa.current;return null!==e?e:iu.pooledCache}function Qa(e,t){q(qa,null===t?qa.current:t.pool)}function Za(){var e=Wa();return null===e?null:{parent:Ra._currentValue,pool:e}}var Ya=Error(r(460)),Ga=Error(r(474)),Ka=Error(r(542)),Xa={then:function(){}};function Ja(e){return"fulfilled"===(e=e.status)||"rejected"===e}function el(){}function tl(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(el,el),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw al(e=t.reason),e;default:if("string"==typeof t.status)t.then(el,el);else{if(null!==(e=iu)&&100<e.shellSuspendCounter)throw Error(r(482));(e=t).status="pending",e.then((function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}}),(function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}}))}switch(t.status){case"fulfilled":return t.value;case"rejected":throw al(e=t.reason),e}throw nl=t,Ya}}var nl=null;function rl(){if(null===nl)throw Error(r(459));var e=nl;return nl=null,e}function al(e){if(e===Ya||e===Ka)throw Error(r(483))}var ll=!1;function sl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function il(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ol(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function ul(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,2&su){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,t=Dr(e),Rr(e,null,n),t}return Or(e,r,t,n),Dr(e)}function cl(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,_e(e,n)}}function dl(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var s={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===l?a=l=s:l=l.next=s,n=n.next}while(null!==n);null===l?a=l=t:l=l.next=t}else a=l=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:l,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var fl=!1;function ml(){if(fl){if(null!==Va)throw Va}}function pl(e,t,n,r){fl=!1;var a=e.updateQueue;ll=!1;var l=a.firstBaseUpdate,s=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var o=i,c=o.next;o.next=null,null===s?l=c:s.next=c,s=o;var d=e.alternate;null!==d&&((i=(d=d.updateQueue).lastBaseUpdate)!==s&&(null===i?d.firstBaseUpdate=c:i.next=c,d.lastBaseUpdate=o))}if(null!==l){var f=a.baseState;for(s=0,d=c=o=null,i=l;;){var m=-536870913&i.lane,p=m!==i.lane;if(p?(uu&m)===m:(r&m)===m){0!==m&&m===Ua&&(fl=!0),null!==d&&(d=d.next={lane:0,tag:i.tag,payload:i.payload,callback:null,next:null});e:{var h=e,g=i;m=t;var b=n;switch(g.tag){case 1:if("function"==typeof(h=g.payload)){f=h.call(b,f,m);break e}f=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null==(m="function"==typeof(h=g.payload)?h.call(b,f,m):h))break e;f=u({},f,m);break e;case 2:ll=!0}}null!==(m=i.callback)&&(e.flags|=64,p&&(e.flags|=8192),null===(p=a.callbacks)?a.callbacks=[m]:p.push(m))}else p={lane:m,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===d?(c=d=p,o=f):d=d.next=p,s|=m;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(p=i).next,p.next=null,a.lastBaseUpdate=p,a.shared.pending=null}}null===d&&(o=f),a.baseState=o,a.firstBaseUpdate=c,a.lastBaseUpdate=d,null===l&&(a.shared.lanes=0),bu|=s,e.lanes=s,e.memoizedState=f}}function hl(e,t){if("function"!=typeof e)throw Error(r(191,e));e.call(t)}function gl(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)hl(n[e],t)}var bl=B(null),vl=B(0);function yl(e,t){q(vl,e=hu),q(bl,t),hu=e|t.baseLanes}function xl(){q(vl,hu),q(bl,bl.current)}function wl(){hu=vl.current,H(bl),H(vl)}var kl=0,Sl=null,Nl=null,jl=null,Cl=!1,El=!1,Tl=!1,Pl=0,zl=0,_l=null,Ll=0;function Ol(){throw Error(r(321))}function Ml(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Jn(e[n],t[n]))return!1;return!0}function Fl(e,t,n,r,a,l){return kl=l,Sl=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,A.H=null===e||null===e.memoizedState?Ys:Gs,Tl=!1,l=n(r,a),Tl=!1,El&&(l=Dl(t,n,r,a)),Rl(e),l}function Rl(e){A.H=Zs;var t=null!==Nl&&null!==Nl.next;if(kl=0,jl=Nl=Sl=null,Cl=!1,zl=0,_l=null,t)throw Error(r(300));null===e||zi||null!==(e=e.dependencies)&&Ta(e)&&(zi=!0)}function Dl(e,t,n,a){Sl=e;var l=0;do{if(El&&(_l=null),zl=0,El=!1,25<=l)throw Error(r(301));if(l+=1,jl=Nl=null,null!=e.updateQueue){var s=e.updateQueue;s.lastEffect=null,s.events=null,s.stores=null,null!=s.memoCache&&(s.memoCache.index=0)}A.H=Ks,s=t(n,a)}while(El);return s}function Al(){var e=A.H,t=e.useState()[0];return t="function"==typeof t.then?Hl(t):t,e=e.useState()[0],(null!==Nl?Nl.memoizedState:null)!==e&&(Sl.flags|=1024),t}function Il(){var e=0!==Pl;return Pl=0,e}function $l(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Ul(e){if(Cl){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}Cl=!1}kl=0,jl=Nl=Sl=null,El=!1,zl=Pl=0,_l=null}function Vl(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===jl?Sl.memoizedState=jl=e:jl=jl.next=e,jl}function Bl(){if(null===Nl){var e=Sl.alternate;e=null!==e?e.memoizedState:null}else e=Nl.next;var t=null===jl?Sl.memoizedState:jl.next;if(null!==t)jl=t,Nl=e;else{if(null===e){if(null===Sl.alternate)throw Error(r(467));throw Error(r(310))}e={memoizedState:(Nl=e).memoizedState,baseState:Nl.baseState,baseQueue:Nl.baseQueue,queue:Nl.queue,next:null},null===jl?Sl.memoizedState=jl=e:jl=jl.next=e}return jl}function Hl(e){var t=zl;return zl+=1,null===_l&&(_l=[]),e=tl(_l,e,t),t=Sl,null===(null===jl?t.memoizedState:jl.next)&&(t=t.alternate,A.H=null===t||null===t.memoizedState?Ys:Gs),e}function ql(e){if(null!==e&&"object"==typeof e){if("function"==typeof e.then)return Hl(e);if(e.$$typeof===w)return za(e)}throw Error(r(438,String(e)))}function Wl(e){var t=null,n=Sl.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=Sl.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(t={data:r.data.map((function(e){return e.slice()})),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},Sl.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=L;return t.index++,n}function Ql(e,t){return"function"==typeof t?t(e):t}function Zl(e){return Yl(Bl(),Nl,e)}function Yl(e,t,n){var a=e.queue;if(null===a)throw Error(r(311));a.lastRenderedReducer=n;var l=e.baseQueue,s=a.pending;if(null!==s){if(null!==l){var i=l.next;l.next=s.next,s.next=i}t.baseQueue=l=s,a.pending=null}if(s=e.baseState,null===l)e.memoizedState=s;else{var o=i=null,u=null,c=t=l.next,d=!1;do{var f=-536870913&c.lane;if(f!==c.lane?(uu&f)===f:(kl&f)===f){var m=c.revertLane;if(0===m)null!==u&&(u=u.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),f===Ua&&(d=!0);else{if((kl&m)===m){c=c.next,m===Ua&&(d=!0);continue}f={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(o=u=f,i=s):u=u.next=f,Sl.lanes|=m,bu|=m}f=c.action,Tl&&n(s,f),s=c.hasEagerState?c.eagerState:n(s,f)}else m={lane:f,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(o=u=m,i=s):u=u.next=m,Sl.lanes|=f,bu|=f;c=c.next}while(null!==c&&c!==t);if(null===u?i=s:u.next=o,!Jn(s,e.memoizedState)&&(zi=!0,d&&null!==(n=Va)))throw n;e.memoizedState=s,e.baseState=i,e.baseQueue=u,a.lastRenderedState=s}return null===l&&(a.lanes=0),[e.memoizedState,a.dispatch]}function Gl(e){var t=Bl(),n=t.queue;if(null===n)throw Error(r(311));n.lastRenderedReducer=e;var a=n.dispatch,l=n.pending,s=t.memoizedState;if(null!==l){n.pending=null;var i=l=l.next;do{s=e(s,i.action),i=i.next}while(i!==l);Jn(s,t.memoizedState)||(zi=!0),t.memoizedState=s,null===t.baseQueue&&(t.baseState=s),n.lastRenderedState=s}return[s,a]}function Kl(e,t,n){var a=Sl,l=Bl(),s=ua;if(s){if(void 0===n)throw Error(r(407));n=n()}else n=t();var i=!Jn((Nl||l).memoizedState,n);if(i&&(l.memoizedState=n,zi=!0),l=l.queue,xs(2048,8,es.bind(null,a,l,e),[e]),l.getSnapshot!==t||i||null!==jl&&1&jl.memoizedState.tag){if(a.flags|=2048,bs(9,{destroy:void 0,resource:void 0},Jl.bind(null,a,l,n,t),null),null===iu)throw Error(r(349));s||124&kl||Xl(a,t,n)}return n}function Xl(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=Sl.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},Sl.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Jl(e,t,n,r){t.value=n,t.getSnapshot=r,ts(t)&&ns(e)}function es(e,t,n){return n((function(){ts(t)&&ns(e)}))}function ts(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Jn(e,n)}catch(r){return!0}}function ns(e){var t=Fr(e,2);null!==t&&$u(t,e,2)}function rs(e){var t=Vl();if("function"==typeof e){var n=e;if(e=n(),Tl){ge(!0);try{n()}finally{ge(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ql,lastRenderedState:e},t}function as(e,t,n,r){return e.baseState=n,Yl(e,Nl,"function"==typeof r?r:Ql)}function ls(e,t,n,a,l){if(qs(e))throw Error(r(485));if(null!==(e=t.action)){var s={payload:l,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){s.listeners.push(e)}};null!==A.T?n(!0):s.isTransition=!1,a(s),null===(n=t.pending)?(s.next=t.pending=s,ss(t,s)):(s.next=n.next,t.pending=n.next=s)}}function ss(e,t){var n=t.action,r=t.payload,a=e.state;if(t.isTransition){var l=A.T,s={};A.T=s;try{var i=n(a,r),o=A.S;null!==o&&o(s,i),is(e,t,i)}catch(u){us(e,t,u)}finally{A.T=l}}else try{is(e,t,l=n(a,r))}catch(c){us(e,t,c)}}function is(e,t,n){null!==n&&"object"==typeof n&&"function"==typeof n.then?n.then((function(n){os(e,t,n)}),(function(n){return us(e,t,n)})):os(e,t,n)}function os(e,t,n){t.status="fulfilled",t.value=n,cs(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,ss(e,n)))}function us(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,cs(t),t=t.next}while(t!==r)}e.action=null}function cs(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function ds(e,t){return t}function fs(e,t){if(ua){var n=iu.formState;if(null!==n){e:{var r=Sl;if(ua){if(oa){t:{for(var a=oa,l=da;8!==a.nodeType;){if(!l){a=null;break t}if(null===(a=wd(a.nextSibling))){a=null;break t}}a="F!"===(l=a.data)||"F"===l?a:null}if(a){oa=wd(a.nextSibling),r="F!"===a.data;break e}}ma(r)}r=!1}r&&(t=n[0])}}return(n=Vl()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ds,lastRenderedState:t},n.queue=r,n=Vs.bind(null,Sl,r),r.dispatch=n,r=rs(!1),l=Hs.bind(null,Sl,!1,r.queue),a={state:t,dispatch:null,action:e,pending:null},(r=Vl()).queue=a,n=ls.bind(null,Sl,a,l,n),a.dispatch=n,r.memoizedState=e,[t,n,!1]}function ms(e){return ps(Bl(),Nl,e)}function ps(e,t,n){if(t=Yl(e,t,ds)[0],e=Zl(Ql)[0],"object"==typeof t&&null!==t&&"function"==typeof t.then)try{var r=Hl(t)}catch(s){if(s===Ya)throw Ka;throw s}else r=t;var a=(t=Bl()).queue,l=a.dispatch;return n!==t.memoizedState&&(Sl.flags|=2048,bs(9,{destroy:void 0,resource:void 0},hs.bind(null,a,n),null)),[r,l,e]}function hs(e,t){e.action=t}function gs(e){var t=Bl(),n=Nl;if(null!==n)return ps(t,n,e);Bl(),t=t.memoizedState;var r=(n=Bl()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function bs(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=Sl.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},Sl.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function vs(){return Bl().memoizedState}function ys(e,t,n,r){var a=Vl();r=void 0===r?null:r,Sl.flags|=e,a.memoizedState=bs(1|t,{destroy:void 0,resource:void 0},n,r)}function xs(e,t,n,r){var a=Bl();r=void 0===r?null:r;var l=a.memoizedState.inst;null!==Nl&&null!==r&&Ml(r,Nl.memoizedState.deps)?a.memoizedState=bs(t,l,n,r):(Sl.flags|=e,a.memoizedState=bs(1|t,l,n,r))}function ws(e,t){ys(8390656,8,e,t)}function ks(e,t){xs(2048,8,e,t)}function Ss(e,t){return xs(4,2,e,t)}function Ns(e,t){return xs(4,4,e,t)}function js(e,t){if("function"==typeof t){e=e();var n=t(e);return function(){"function"==typeof n?n():t(null)}}if(null!=t)return e=e(),t.current=e,function(){t.current=null}}function Cs(e,t,n){n=null!=n?n.concat([e]):null,xs(4,4,js.bind(null,t,e),n)}function Es(){}function Ts(e,t){var n=Bl();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&Ml(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ps(e,t){var n=Bl();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&Ml(t,r[1]))return r[0];if(r=e(),Tl){ge(!0);try{e()}finally{ge(!1)}}return n.memoizedState=[r,t],r}function zs(e,t,n){return void 0===n||1073741824&kl?e.memoizedState=t:(e.memoizedState=n,e=Iu(),Sl.lanes|=e,bu|=e,n)}function _s(e,t,n,r){return Jn(n,t)?n:null!==bl.current?(e=zs(e,n,r),Jn(e,t)||(zi=!0),e):42&kl?(e=Iu(),Sl.lanes|=e,bu|=e,t):(zi=!0,e.memoizedState=n)}function Ls(e,t,n,r,a){var l=I.p;I.p=0!==l&&8>l?l:8;var s,i,o,u=A.T,c={};A.T=c,Hs(e,!1,t,n);try{var d=a(),f=A.S;if(null!==f&&f(c,d),null!==d&&"object"==typeof d&&"function"==typeof d.then)Bs(e,t,(s=r,i=[],o={status:"pending",value:null,reason:null,then:function(e){i.push(e)}},d.then((function(){o.status="fulfilled",o.value=s;for(var e=0;e<i.length;e++)(0,i[e])(s)}),(function(e){for(o.status="rejected",o.reason=e,e=0;e<i.length;e++)(0,i[e])(void 0)})),o),Au());else Bs(e,t,r,Au())}catch(m){Bs(e,t,{then:function(){},status:"rejected",reason:m},Au())}finally{I.p=l,A.T=u}}function Os(){}function Ms(e,t,n,a){if(5!==e.tag)throw Error(r(476));var l=Fs(e).queue;Ls(e,l,t,$,null===n?Os:function(){return Rs(e),n(a)})}function Fs(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:$,baseState:$,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ql,lastRenderedState:$},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ql,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function Rs(e){Bs(e,Fs(e).next.queue,{},Au())}function Ds(){return za(Xd)}function As(){return Bl().memoizedState}function Is(){return Bl().memoizedState}function $s(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=Au(),r=ul(t,e=ol(n),n);return null!==r&&($u(r,t,n),cl(r,t,n)),t={cache:Da()},void(e.payload=t)}t=t.return}}function Us(e,t,n){var r=Au();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},qs(e)?Ws(t,n):null!==(n=Mr(e,t,n,r))&&($u(n,e,r),Qs(n,t,r))}function Vs(e,t,n){Bs(e,t,n,Au())}function Bs(e,t,n,r){var a={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(qs(e))Ws(t,a);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var s=t.lastRenderedState,i=l(s,n);if(a.hasEagerState=!0,a.eagerState=i,Jn(i,s))return Or(e,t,a,0),null===iu&&Lr(),!1}catch(o){}if(null!==(n=Mr(e,t,a,r)))return $u(n,e,r),Qs(n,t,r),!0}return!1}function Hs(e,t,n,a){if(a={lane:2,revertLane:Oc(),action:a,hasEagerState:!1,eagerState:null,next:null},qs(e)){if(t)throw Error(r(479))}else null!==(t=Mr(e,n,a,2))&&$u(t,e,2)}function qs(e){var t=e.alternate;return e===Sl||null!==t&&t===Sl}function Ws(e,t){El=Cl=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Qs(e,t,n){if(4194048&n){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,_e(e,n)}}var Zs={readContext:za,use:ql,useCallback:Ol,useContext:Ol,useEffect:Ol,useImperativeHandle:Ol,useLayoutEffect:Ol,useInsertionEffect:Ol,useMemo:Ol,useReducer:Ol,useRef:Ol,useState:Ol,useDebugValue:Ol,useDeferredValue:Ol,useTransition:Ol,useSyncExternalStore:Ol,useId:Ol,useHostTransitionStatus:Ol,useFormState:Ol,useActionState:Ol,useOptimistic:Ol,useMemoCache:Ol,useCacheRefresh:Ol},Ys={readContext:za,use:ql,useCallback:function(e,t){return Vl().memoizedState=[e,void 0===t?null:t],e},useContext:za,useEffect:ws,useImperativeHandle:function(e,t,n){n=null!=n?n.concat([e]):null,ys(4194308,4,js.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ys(4194308,4,e,t)},useInsertionEffect:function(e,t){ys(4,2,e,t)},useMemo:function(e,t){var n=Vl();t=void 0===t?null:t;var r=e();if(Tl){ge(!0);try{e()}finally{ge(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=Vl();if(void 0!==n){var a=n(t);if(Tl){ge(!0);try{n(t)}finally{ge(!1)}}}else a=t;return r.memoizedState=r.baseState=a,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:a},r.queue=e,e=e.dispatch=Us.bind(null,Sl,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Vl().memoizedState=e},useState:function(e){var t=(e=rs(e)).queue,n=Vs.bind(null,Sl,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Es,useDeferredValue:function(e,t){return zs(Vl(),e,t)},useTransition:function(){var e=rs(!1);return e=Ls.bind(null,Sl,e.queue,!0,!1),Vl().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var a=Sl,l=Vl();if(ua){if(void 0===n)throw Error(r(407));n=n()}else{if(n=t(),null===iu)throw Error(r(349));124&uu||Xl(a,t,n)}l.memoizedState=n;var s={value:n,getSnapshot:t};return l.queue=s,ws(es.bind(null,a,s,e),[e]),a.flags|=2048,bs(9,{destroy:void 0,resource:void 0},Jl.bind(null,a,s,n,t),null),n},useId:function(){var e=Vl(),t=iu.identifierPrefix;if(ua){var n=na;t="«"+t+"R"+(n=(ta&~(1<<32-be(ta)-1)).toString(32)+n),0<(n=Pl++)&&(t+="H"+n.toString(32)),t+="»"}else t="«"+t+"r"+(n=Ll++).toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Ds,useFormState:fs,useActionState:fs,useOptimistic:function(e){var t=Vl();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Hs.bind(null,Sl,!0,n),n.dispatch=t,[e,t]},useMemoCache:Wl,useCacheRefresh:function(){return Vl().memoizedState=$s.bind(null,Sl)}},Gs={readContext:za,use:ql,useCallback:Ts,useContext:za,useEffect:ks,useImperativeHandle:Cs,useInsertionEffect:Ss,useLayoutEffect:Ns,useMemo:Ps,useReducer:Zl,useRef:vs,useState:function(){return Zl(Ql)},useDebugValue:Es,useDeferredValue:function(e,t){return _s(Bl(),Nl.memoizedState,e,t)},useTransition:function(){var e=Zl(Ql)[0],t=Bl().memoizedState;return["boolean"==typeof e?e:Hl(e),t]},useSyncExternalStore:Kl,useId:As,useHostTransitionStatus:Ds,useFormState:ms,useActionState:ms,useOptimistic:function(e,t){return as(Bl(),0,e,t)},useMemoCache:Wl,useCacheRefresh:Is},Ks={readContext:za,use:ql,useCallback:Ts,useContext:za,useEffect:ks,useImperativeHandle:Cs,useInsertionEffect:Ss,useLayoutEffect:Ns,useMemo:Ps,useReducer:Gl,useRef:vs,useState:function(){return Gl(Ql)},useDebugValue:Es,useDeferredValue:function(e,t){var n=Bl();return null===Nl?zs(n,e,t):_s(n,Nl.memoizedState,e,t)},useTransition:function(){var e=Gl(Ql)[0],t=Bl().memoizedState;return["boolean"==typeof e?e:Hl(e),t]},useSyncExternalStore:Kl,useId:As,useHostTransitionStatus:Ds,useFormState:gs,useActionState:gs,useOptimistic:function(e,t){var n=Bl();return null!==Nl?as(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Wl,useCacheRefresh:Is},Xs=null,Js=0;function ei(e){var t=Js;return Js+=1,null===Xs&&(Xs=[]),tl(Xs,e,t)}function ti(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function ni(e,t){if(t.$$typeof===c)throw Error(r(525));throw e=Object.prototype.toString.call(t),Error(r(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ri(e){return(0,e._init)(e._payload)}function ai(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function a(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function l(e,t){return(e=Vr(e,t)).index=0,e.sibling=null,e}function s(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function o(e,t,n,r){return null===t||6!==t.tag?((t=Wr(n,e.mode,r)).return=e,t):((t=l(t,n)).return=e,t)}function u(e,t,n,r){var a=n.type;return a===p?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===a||"object"==typeof a&&null!==a&&a.$$typeof===z&&ri(a)===t.type)?(ti(t=l(t,n.props),n),t.return=e,t):(ti(t=Hr(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Qr(n,e.mode,r)).return=e,t):((t=l(t,n.children||[])).return=e,t)}function d(e,t,n,r,a){return null===t||7!==t.tag?((t=qr(n,e.mode,r,a)).return=e,t):((t=l(t,n)).return=e,t)}function h(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t||"bigint"==typeof t)return(t=Wr(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case f:return ti(n=Hr(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case m:return(t=Qr(t,e.mode,n)).return=e,t;case z:return h(e,t=(0,t._init)(t._payload),n)}if(D(t)||M(t))return(t=qr(t,e.mode,n,null)).return=e,t;if("function"==typeof t.then)return h(e,ei(t),n);if(t.$$typeof===w)return h(e,_a(e,t),n);ni(e,t)}return null}function g(e,t,n,r){var a=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n||"bigint"==typeof n)return null!==a?null:o(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case f:return n.key===a?u(e,t,n,r):null;case m:return n.key===a?c(e,t,n,r):null;case z:return g(e,t,n=(a=n._init)(n._payload),r)}if(D(n)||M(n))return null!==a?null:d(e,t,n,r,null);if("function"==typeof n.then)return g(e,t,ei(n),r);if(n.$$typeof===w)return g(e,t,_a(e,n),r);ni(e,n)}return null}function b(e,t,n,r,a){if("string"==typeof r&&""!==r||"number"==typeof r||"bigint"==typeof r)return o(t,e=e.get(n)||null,""+r,a);if("object"==typeof r&&null!==r){switch(r.$$typeof){case f:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case m:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case z:return b(e,t,n,r=(0,r._init)(r._payload),a)}if(D(r)||M(r))return d(t,e=e.get(n)||null,r,a,null);if("function"==typeof r.then)return b(e,t,n,ei(r),a);if(r.$$typeof===w)return b(e,t,n,_a(t,r),a);ni(t,r)}return null}function v(o,u,c,d){if("object"==typeof c&&null!==c&&c.type===p&&null===c.key&&(c=c.props.children),"object"==typeof c&&null!==c){switch(c.$$typeof){case f:e:{for(var y=c.key;null!==u;){if(u.key===y){if((y=c.type)===p){if(7===u.tag){n(o,u.sibling),(d=l(u,c.props.children)).return=o,o=d;break e}}else if(u.elementType===y||"object"==typeof y&&null!==y&&y.$$typeof===z&&ri(y)===u.type){n(o,u.sibling),ti(d=l(u,c.props),c),d.return=o,o=d;break e}n(o,u);break}t(o,u),u=u.sibling}c.type===p?((d=qr(c.props.children,o.mode,d,c.key)).return=o,o=d):(ti(d=Hr(c.type,c.key,c.props,null,o.mode,d),c),d.return=o,o=d)}return i(o);case m:e:{for(y=c.key;null!==u;){if(u.key===y){if(4===u.tag&&u.stateNode.containerInfo===c.containerInfo&&u.stateNode.implementation===c.implementation){n(o,u.sibling),(d=l(u,c.children||[])).return=o,o=d;break e}n(o,u);break}t(o,u),u=u.sibling}(d=Qr(c,o.mode,d)).return=o,o=d}return i(o);case z:return v(o,u,c=(y=c._init)(c._payload),d)}if(D(c))return function(r,l,i,o){for(var u=null,c=null,d=l,f=l=0,m=null;null!==d&&f<i.length;f++){d.index>f?(m=d,d=null):m=d.sibling;var p=g(r,d,i[f],o);if(null===p){null===d&&(d=m);break}e&&d&&null===p.alternate&&t(r,d),l=s(p,l,f),null===c?u=p:c.sibling=p,c=p,d=m}if(f===i.length)return n(r,d),ua&&ra(r,f),u;if(null===d){for(;f<i.length;f++)null!==(d=h(r,i[f],o))&&(l=s(d,l,f),null===c?u=d:c.sibling=d,c=d);return ua&&ra(r,f),u}for(d=a(d);f<i.length;f++)null!==(m=b(d,r,f,i[f],o))&&(e&&null!==m.alternate&&d.delete(null===m.key?f:m.key),l=s(m,l,f),null===c?u=m:c.sibling=m,c=m);return e&&d.forEach((function(e){return t(r,e)})),ua&&ra(r,f),u}(o,u,c,d);if(M(c)){if("function"!=typeof(y=M(c)))throw Error(r(150));return function(l,i,o,u){if(null==o)throw Error(r(151));for(var c=null,d=null,f=i,m=i=0,p=null,v=o.next();null!==f&&!v.done;m++,v=o.next()){f.index>m?(p=f,f=null):p=f.sibling;var y=g(l,f,v.value,u);if(null===y){null===f&&(f=p);break}e&&f&&null===y.alternate&&t(l,f),i=s(y,i,m),null===d?c=y:d.sibling=y,d=y,f=p}if(v.done)return n(l,f),ua&&ra(l,m),c;if(null===f){for(;!v.done;m++,v=o.next())null!==(v=h(l,v.value,u))&&(i=s(v,i,m),null===d?c=v:d.sibling=v,d=v);return ua&&ra(l,m),c}for(f=a(f);!v.done;m++,v=o.next())null!==(v=b(f,l,m,v.value,u))&&(e&&null!==v.alternate&&f.delete(null===v.key?m:v.key),i=s(v,i,m),null===d?c=v:d.sibling=v,d=v);return e&&f.forEach((function(e){return t(l,e)})),ua&&ra(l,m),c}(o,u,c=y.call(c),d)}if("function"==typeof c.then)return v(o,u,ei(c),d);if(c.$$typeof===w)return v(o,u,_a(o,c),d);ni(o,c)}return"string"==typeof c&&""!==c||"number"==typeof c||"bigint"==typeof c?(c=""+c,null!==u&&6===u.tag?(n(o,u.sibling),(d=l(u,c)).return=o,o=d):(n(o,u),(d=Wr(c,o.mode,d)).return=o,o=d),i(o)):n(o,u)}return function(e,t,n,r){try{Js=0;var a=v(e,t,n,r);return Xs=null,a}catch(s){if(s===Ya||s===Ka)throw s;var l=$r(29,s,null,e.mode);return l.lanes=r,l.return=e,l}}}var li=ai(!0),si=ai(!1),ii=B(null),oi=null;function ui(e){var t=e.alternate;q(mi,1&mi.current),q(ii,e),null===oi&&(null===t||null!==bl.current||null!==t.memoizedState)&&(oi=e)}function ci(e){if(22===e.tag){if(q(mi,mi.current),q(ii,e),null===oi){var t=e.alternate;null!==t&&null!==t.memoizedState&&(oi=e)}}else di()}function di(){q(mi,mi.current),q(ii,ii.current)}function fi(e){H(ii),oi===e&&(oi=null),H(mi)}var mi=B(0);function pi(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||xd(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function hi(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:u({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var gi={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Au(),a=ol(r);a.payload=t,null!=n&&(a.callback=n),null!==(t=ul(e,a,r))&&($u(t,e,r),cl(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Au(),a=ol(r);a.tag=1,a.payload=t,null!=n&&(a.callback=n),null!==(t=ul(e,a,r))&&($u(t,e,r),cl(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Au(),r=ol(n);r.tag=2,null!=t&&(r.callback=t),null!==(t=ul(e,r,n))&&($u(t,e,n),cl(t,e,n))}};function bi(e,t,n,r,a,l,s){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,l,s):!t.prototype||!t.prototype.isPureReactComponent||(!er(n,r)||!er(a,l))}function vi(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&gi.enqueueReplaceState(t,t.state,null)}function yi(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var a in n===t&&(n=u({},n)),e)void 0===n[a]&&(n[a]=e[a]);return n}var xi="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e)};function wi(e){xi(e)}function ki(e){}function Si(e){xi(e)}function Ni(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout((function(){throw n}))}}function ji(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(r){setTimeout((function(){throw r}))}}function Ci(e,t,n){return(n=ol(n)).tag=3,n.payload={element:null},n.callback=function(){Ni(e,t)},n}function Ei(e){return(e=ol(e)).tag=3,e}function Ti(e,t,n,r){var a=n.type.getDerivedStateFromError;if("function"==typeof a){var l=r.value;e.payload=function(){return a(l)},e.callback=function(){ji(t,n,r)}}var s=n.stateNode;null!==s&&"function"==typeof s.componentDidCatch&&(e.callback=function(){ji(t,n,r),"function"!=typeof a&&(null===Tu?Tu=new Set([this]):Tu.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var Pi=Error(r(461)),zi=!1;function _i(e,t,n,r){t.child=null===e?si(t,null,n,r):li(t,e.child,n,r)}function Li(e,t,n,r,a){n=n.render;var l=t.ref;if("ref"in r){var s={};for(var i in r)"ref"!==i&&(s[i]=r[i])}else s=r;return Pa(t),r=Fl(e,t,n,s,l,a),i=Il(),null===e||zi?(ua&&i&&la(t),t.flags|=1,_i(e,t,r,a),t.child):($l(e,t,a),Xi(e,t,a))}function Oi(e,t,n,r,a){if(null===e){var l=n.type;return"function"!=typeof l||Ur(l)||void 0!==l.defaultProps||null!==n.compare?((e=Hr(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,Mi(e,t,l,r,a))}if(l=e.child,!Ji(e,a)){var s=l.memoizedProps;if((n=null!==(n=n.compare)?n:er)(s,r)&&e.ref===t.ref)return Xi(e,t,a)}return t.flags|=1,(e=Vr(l,r)).ref=t.ref,e.return=t,t.child=e}function Mi(e,t,n,r,a){if(null!==e){var l=e.memoizedProps;if(er(l,r)&&e.ref===t.ref){if(zi=!1,t.pendingProps=r=l,!Ji(e,a))return t.lanes=e.lanes,Xi(e,t,a);131072&e.flags&&(zi=!0)}}return Ai(e,t,n,r,a)}function Fi(e,t,n){var r=t.pendingProps,a=r.children,l=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(128&t.flags){if(r=null!==l?l.baseLanes|n:n,null!==e){for(a=t.child=e.child,l=0;null!==a;)l=l|a.lanes|a.childLanes,a=a.sibling;t.childLanes=l&~r}else t.childLanes=0,t.child=null;return Ri(e,t,r,n)}if(!(536870912&n))return t.lanes=t.childLanes=536870912,Ri(e,t,null!==l?l.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&Qa(0,null!==l?l.cachePool:null),null!==l?yl(t,l):xl(),ci(t)}else null!==l?(Qa(0,l.cachePool),yl(t,l),di(),t.memoizedState=null):(null!==e&&Qa(0,null),xl(),di());return _i(e,t,a,n),t.child}function Ri(e,t,n,r){var a=Wa();return a=null===a?null:{parent:Ra._currentValue,pool:a},t.memoizedState={baseLanes:n,cachePool:a},null!==e&&Qa(0,null),xl(),ci(t),null!==e&&Ea(e,t,r,!0),null}function Di(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!=typeof n&&"object"!=typeof n)throw Error(r(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function Ai(e,t,n,r,a){return Pa(t),n=Fl(e,t,n,r,void 0,a),r=Il(),null===e||zi?(ua&&r&&la(t),t.flags|=1,_i(e,t,n,a),t.child):($l(e,t,a),Xi(e,t,a))}function Ii(e,t,n,r,a,l){return Pa(t),t.updateQueue=null,n=Dl(t,r,n,a),Rl(e),r=Il(),null===e||zi?(ua&&r&&la(t),t.flags|=1,_i(e,t,n,l),t.child):($l(e,t,l),Xi(e,t,l))}function $i(e,t,n,r,a){if(Pa(t),null===t.stateNode){var l=Ar,s=n.contextType;"object"==typeof s&&null!==s&&(l=za(s)),l=new n(r,l),t.memoizedState=null!==l.state&&void 0!==l.state?l.state:null,l.updater=gi,t.stateNode=l,l._reactInternals=t,(l=t.stateNode).props=r,l.state=t.memoizedState,l.refs={},sl(t),s=n.contextType,l.context="object"==typeof s&&null!==s?za(s):Ar,l.state=t.memoizedState,"function"==typeof(s=n.getDerivedStateFromProps)&&(hi(t,n,s,r),l.state=t.memoizedState),"function"==typeof n.getDerivedStateFromProps||"function"==typeof l.getSnapshotBeforeUpdate||"function"!=typeof l.UNSAFE_componentWillMount&&"function"!=typeof l.componentWillMount||(s=l.state,"function"==typeof l.componentWillMount&&l.componentWillMount(),"function"==typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount(),s!==l.state&&gi.enqueueReplaceState(l,l.state,null),pl(t,r,l,a),ml(),l.state=t.memoizedState),"function"==typeof l.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){l=t.stateNode;var i=t.memoizedProps,o=yi(n,i);l.props=o;var u=l.context,c=n.contextType;s=Ar,"object"==typeof c&&null!==c&&(s=za(c));var d=n.getDerivedStateFromProps;c="function"==typeof d||"function"==typeof l.getSnapshotBeforeUpdate,i=t.pendingProps!==i,c||"function"!=typeof l.UNSAFE_componentWillReceiveProps&&"function"!=typeof l.componentWillReceiveProps||(i||u!==s)&&vi(t,l,r,s),ll=!1;var f=t.memoizedState;l.state=f,pl(t,r,l,a),ml(),u=t.memoizedState,i||f!==u||ll?("function"==typeof d&&(hi(t,n,d,r),u=t.memoizedState),(o=ll||bi(t,n,o,r,f,u,s))?(c||"function"!=typeof l.UNSAFE_componentWillMount&&"function"!=typeof l.componentWillMount||("function"==typeof l.componentWillMount&&l.componentWillMount(),"function"==typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount()),"function"==typeof l.componentDidMount&&(t.flags|=4194308)):("function"==typeof l.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),l.props=r,l.state=u,l.context=s,r=o):("function"==typeof l.componentDidMount&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,il(e,t),c=yi(n,s=t.memoizedProps),l.props=c,d=t.pendingProps,f=l.context,u=n.contextType,o=Ar,"object"==typeof u&&null!==u&&(o=za(u)),(u="function"==typeof(i=n.getDerivedStateFromProps)||"function"==typeof l.getSnapshotBeforeUpdate)||"function"!=typeof l.UNSAFE_componentWillReceiveProps&&"function"!=typeof l.componentWillReceiveProps||(s!==d||f!==o)&&vi(t,l,r,o),ll=!1,f=t.memoizedState,l.state=f,pl(t,r,l,a),ml();var m=t.memoizedState;s!==d||f!==m||ll||null!==e&&null!==e.dependencies&&Ta(e.dependencies)?("function"==typeof i&&(hi(t,n,i,r),m=t.memoizedState),(c=ll||bi(t,n,c,r,f,m,o)||null!==e&&null!==e.dependencies&&Ta(e.dependencies))?(u||"function"!=typeof l.UNSAFE_componentWillUpdate&&"function"!=typeof l.componentWillUpdate||("function"==typeof l.componentWillUpdate&&l.componentWillUpdate(r,m,o),"function"==typeof l.UNSAFE_componentWillUpdate&&l.UNSAFE_componentWillUpdate(r,m,o)),"function"==typeof l.componentDidUpdate&&(t.flags|=4),"function"==typeof l.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof l.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof l.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=m),l.props=r,l.state=m,l.context=o,r=c):("function"!=typeof l.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof l.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return l=r,Di(e,t),r=!!(128&t.flags),l||r?(l=t.stateNode,n=r&&"function"!=typeof n.getDerivedStateFromError?null:l.render(),t.flags|=1,null!==e&&r?(t.child=li(t,e.child,null,a),t.child=li(t,null,n,a)):_i(e,t,n,a),t.memoizedState=l.state,e=t.child):e=Xi(e,t,a),e}function Ui(e,t,n,r){return ba(),t.flags|=256,_i(e,t,n,r),t.child}var Vi={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Bi(e){return{baseLanes:e,cachePool:Za()}}function Hi(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=xu),e}function qi(e,t,n){var a,l=t.pendingProps,s=!1,i=!!(128&t.flags);if((a=i)||(a=(null===e||null!==e.memoizedState)&&!!(2&mi.current)),a&&(s=!0,t.flags&=-129),a=!!(32&t.flags),t.flags&=-33,null===e){if(ua){if(s?ui(t):di(),ua){var o,u=oa;if(o=u){e:{for(o=u,u=da;8!==o.nodeType;){if(!u){u=null;break e}if(null===(o=wd(o.nextSibling))){u=null;break e}}u=o}null!==u?(t.memoizedState={dehydrated:u,treeContext:null!==ea?{id:ta,overflow:na}:null,retryLane:536870912,hydrationErrors:null},(o=$r(18,null,null,0)).stateNode=u,o.return=t,t.child=o,ia=t,oa=null,o=!0):o=!1}o||ma(t)}if(null!==(u=t.memoizedState)&&null!==(u=u.dehydrated))return xd(u)?t.lanes=32:t.lanes=536870912,null;fi(t)}return u=l.children,l=l.fallback,s?(di(),u=Qi({mode:"hidden",children:u},s=t.mode),l=qr(l,s,n,null),u.return=t,l.return=t,u.sibling=l,t.child=u,(s=t.child).memoizedState=Bi(n),s.childLanes=Hi(e,a,n),t.memoizedState=Vi,l):(ui(t),Wi(t,u))}if(null!==(o=e.memoizedState)&&null!==(u=o.dehydrated)){if(i)256&t.flags?(ui(t),t.flags&=-257,t=Zi(e,t,n)):null!==t.memoizedState?(di(),t.child=e.child,t.flags|=128,t=null):(di(),s=l.fallback,u=t.mode,l=Qi({mode:"visible",children:l.children},u),(s=qr(s,u,n,null)).flags|=2,l.return=t,s.return=t,l.sibling=s,t.child=l,li(t,e.child,null,n),(l=t.child).memoizedState=Bi(n),l.childLanes=Hi(e,a,n),t.memoizedState=Vi,t=s);else if(ui(t),xd(u)){if(a=u.nextSibling&&u.nextSibling.dataset)var c=a.dgst;a=c,(l=Error(r(419))).stack="",l.digest=a,ya({value:l,source:null,stack:null}),t=Zi(e,t,n)}else if(zi||Ea(e,t,n,!1),a=0!==(n&e.childLanes),zi||a){if(null!==(a=iu)&&(0!==(l=0!==((l=42&(l=n&-n)?1:Le(l))&(a.suspendedLanes|n))?0:l)&&l!==o.retryLane))throw o.retryLane=l,Fr(e,l),$u(a,e,l),Pi;"$?"===u.data||Ku(),t=Zi(e,t,n)}else"$?"===u.data?(t.flags|=192,t.child=e.child,t=null):(e=o.treeContext,oa=wd(u.nextSibling),ia=t,ua=!0,ca=null,da=!1,null!==e&&(Xr[Jr++]=ta,Xr[Jr++]=na,Xr[Jr++]=ea,ta=e.id,na=e.overflow,ea=t),(t=Wi(t,l.children)).flags|=4096);return t}return s?(di(),s=l.fallback,u=t.mode,c=(o=e.child).sibling,(l=Vr(o,{mode:"hidden",children:l.children})).subtreeFlags=65011712&o.subtreeFlags,null!==c?s=Vr(c,s):(s=qr(s,u,n,null)).flags|=2,s.return=t,l.return=t,l.sibling=s,t.child=l,l=s,s=t.child,null===(u=e.child.memoizedState)?u=Bi(n):(null!==(o=u.cachePool)?(c=Ra._currentValue,o=o.parent!==c?{parent:c,pool:c}:o):o=Za(),u={baseLanes:u.baseLanes|n,cachePool:o}),s.memoizedState=u,s.childLanes=Hi(e,a,n),t.memoizedState=Vi,l):(ui(t),e=(n=e.child).sibling,(n=Vr(n,{mode:"visible",children:l.children})).return=t,n.sibling=null,null!==e&&(null===(a=t.deletions)?(t.deletions=[e],t.flags|=16):a.push(e)),t.child=n,t.memoizedState=null,n)}function Wi(e,t){return(t=Qi({mode:"visible",children:t},e.mode)).return=e,e.child=t}function Qi(e,t){return(e=$r(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Zi(e,t,n){return li(t,e.child,null,n),(e=Wi(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Yi(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),ja(e.return,t,n)}function Gi(e,t,n,r,a){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=a)}function Ki(e,t,n){var r=t.pendingProps,a=r.revealOrder,l=r.tail;if(_i(e,t,r.children,n),2&(r=mi.current))r=1&r|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Yi(e,n,t);else if(19===e.tag)Yi(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(q(mi,r),a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===pi(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Gi(t,!1,a,n,l);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===pi(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Gi(t,!0,n,null,l);break;case"together":Gi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Xi(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),bu|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(Ea(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(r(153));if(null!==t.child){for(n=Vr(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Vr(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Ji(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!Ta(e))}function eo(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)zi=!0;else{if(!(Ji(e,n)||128&t.flags))return zi=!1,function(e,t,n){switch(t.tag){case 3:G(t,t.stateNode.containerInfo),Sa(0,Ra,e.memoizedState.cache),ba();break;case 27:case 5:X(t);break;case 4:G(t,t.stateNode.containerInfo);break;case 10:Sa(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(ui(t),t.flags|=128,null):0!==(n&t.child.childLanes)?qi(e,t,n):(ui(t),null!==(e=Xi(e,t,n))?e.sibling:null);ui(t);break;case 19:var a=!!(128&e.flags);if((r=0!==(n&t.childLanes))||(Ea(e,t,n,!1),r=0!==(n&t.childLanes)),a){if(r)return Ki(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),q(mi,mi.current),r)break;return null;case 22:case 23:return t.lanes=0,Fi(e,t,n);case 24:Sa(0,Ra,e.memoizedState.cache)}return Xi(e,t,n)}(e,t,n);zi=!!(131072&e.flags)}else zi=!1,ua&&1048576&t.flags&&aa(t,Kr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,l=a._init;if(a=l(a._payload),t.type=a,"function"!=typeof a){if(null!=a){if((l=a.$$typeof)===N){t.tag=11,t=Li(null,t,a,e,n);break e}if(l===P){t.tag=14,t=Oi(null,t,a,e,n);break e}}throw t=R(a)||a,Error(r(306,t,""))}Ur(a)?(e=yi(a,e),t.tag=1,t=$i(null,t,a,e,n)):(t.tag=0,t=Ai(null,t,a,e,n))}return t;case 0:return Ai(e,t,t.type,t.pendingProps,n);case 1:return $i(e,t,a=t.type,l=yi(a,t.pendingProps),n);case 3:e:{if(G(t,t.stateNode.containerInfo),null===e)throw Error(r(387));a=t.pendingProps;var s=t.memoizedState;l=s.element,il(e,t),pl(t,a,null,n);var i=t.memoizedState;if(a=i.cache,Sa(0,Ra,a),a!==s.cache&&Ca(t,[Ra],n,!0),ml(),a=i.element,s.isDehydrated){if(s={element:a,isDehydrated:!1,cache:i.cache},t.updateQueue.baseState=s,t.memoizedState=s,256&t.flags){t=Ui(e,t,a,n);break e}if(a!==l){ya(l=Tr(Error(r(424)),t)),t=Ui(e,t,a,n);break e}if(9===(e=t.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(oa=wd(e.firstChild),ia=t,ua=!0,ca=null,da=!0,n=si(t,null,a,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ba(),a===l){t=Xi(e,t,n);break e}_i(e,t,a,n)}t=t.child}return t;case 26:return Di(e,t),null===e?(n=Ld(t.type,null,t.pendingProps,null))?t.memoizedState=n:ua||(n=t.type,e=t.pendingProps,(a=id(Z.current).createElement(n))[Re]=t,a[De]=e,ad(a,n,e),Ye(a),t.stateNode=a):t.memoizedState=Ld(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return X(t),null===e&&ua&&(a=t.stateNode=Nd(t.type,t.pendingProps,Z.current),ia=t,da=!0,l=oa,bd(t.type)?(kd=l,oa=wd(a.firstChild)):oa=l),_i(e,t,t.pendingProps.children,n),Di(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&ua&&((l=a=oa)&&(null!==(a=function(e,t,n,r){for(;1===e.nodeType;){var a=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[Be])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(l=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(l!==a.rel||e.getAttribute("href")!==(null==a.href||""===a.href?null:a.href)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin)||e.getAttribute("title")!==(null==a.title?null:a.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((l=e.getAttribute("src"))!==(null==a.src?null:a.src)||e.getAttribute("type")!==(null==a.type?null:a.type)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin))&&l&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var l=null==a.name?null:""+a.name;if("hidden"===a.type&&e.getAttribute("name")===l)return e}if(null===(e=wd(e.nextSibling)))break}return null}(a,t.type,t.pendingProps,da))?(t.stateNode=a,ia=t,oa=wd(a.firstChild),da=!1,l=!0):l=!1),l||ma(t)),X(t),l=t.type,s=t.pendingProps,i=null!==e?e.memoizedProps:null,a=s.children,cd(l,s)?a=null:null!==i&&cd(l,i)&&(t.flags|=32),null!==t.memoizedState&&(l=Fl(e,t,Al,null,null,n),Xd._currentValue=l),Di(e,t),_i(e,t,a,n),t.child;case 6:return null===e&&ua&&((e=n=oa)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=wd(e.nextSibling)))return null}return e}(n,t.pendingProps,da))?(t.stateNode=n,ia=t,oa=null,e=!0):e=!1),e||ma(t)),null;case 13:return qi(e,t,n);case 4:return G(t,t.stateNode.containerInfo),a=t.pendingProps,null===e?t.child=li(t,null,a,n):_i(e,t,a,n),t.child;case 11:return Li(e,t,t.type,t.pendingProps,n);case 7:return _i(e,t,t.pendingProps,n),t.child;case 8:case 12:return _i(e,t,t.pendingProps.children,n),t.child;case 10:return a=t.pendingProps,Sa(0,t.type,a.value),_i(e,t,a.children,n),t.child;case 9:return l=t.type._context,a=t.pendingProps.children,Pa(t),a=a(l=za(l)),t.flags|=1,_i(e,t,a,n),t.child;case 14:return Oi(e,t,t.type,t.pendingProps,n);case 15:return Mi(e,t,t.type,t.pendingProps,n);case 19:return Ki(e,t,n);case 31:return a=t.pendingProps,n=t.mode,a={mode:a.mode,children:a.children},null===e?((n=Qi(a,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=Vr(e.child,a)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Fi(e,t,n);case 24:return Pa(t),a=za(Ra),null===e?(null===(l=Wa())&&(l=iu,s=Da(),l.pooledCache=s,s.refCount++,null!==s&&(l.pooledCacheLanes|=n),l=s),t.memoizedState={parent:a,cache:l},sl(t),Sa(0,Ra,l)):(0!==(e.lanes&n)&&(il(e,t),pl(t,null,null,n),ml()),l=e.memoizedState,s=t.memoizedState,l.parent!==a?(l={parent:a,cache:a},t.memoizedState=l,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=l),Sa(0,Ra,a)):(a=s.cache,Sa(0,Ra,a),a!==l.cache&&Ca(t,[Ra],n,!0))),_i(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(r(156,t.tag))}function to(e){e.flags|=4}function no(e,t){if("stylesheet"!==t.type||4&t.state.loading)e.flags&=-16777217;else if(e.flags|=16777216,!qd(t)){if(null!==(t=ii.current)&&((4194048&uu)===uu?null!==oi:(62914560&uu)!==uu&&!(536870912&uu)||t!==oi))throw nl=Xa,Ga;e.flags|=8192}}function ro(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?Ee():536870912,e.lanes|=t,wu|=t)}function ao(e,t){if(!ua)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function lo(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=65011712&a.subtreeFlags,r|=65011712&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function so(e,t,n){var a=t.pendingProps;switch(sa(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return lo(t),null;case 3:return n=t.stateNode,a=null,null!==e&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Na(Ra),K(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(ga(t)?to(t):null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,va())),lo(t),null;case 26:return n=t.memoizedState,null===e?(to(t),null!==n?(lo(t),no(t,n)):(lo(t),t.flags&=-16777217)):n?n!==e.memoizedState?(to(t),lo(t),no(t,n)):(lo(t),t.flags&=-16777217):(e.memoizedProps!==a&&to(t),lo(t),t.flags&=-16777217),null;case 27:J(t),n=Z.current;var l=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==a&&to(t);else{if(!a){if(null===t.stateNode)throw Error(r(166));return lo(t),null}e=W.current,ga(t)?pa(t):(e=Nd(l,a,n),t.stateNode=e,to(t))}return lo(t),null;case 5:if(J(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==a&&to(t);else{if(!a){if(null===t.stateNode)throw Error(r(166));return lo(t),null}if(e=W.current,ga(t))pa(t);else{switch(l=id(Z.current),e){case 1:e=l.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=l.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=l.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=l.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=l.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"==typeof a.is?l.createElement("select",{is:a.is}):l.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e="string"==typeof a.is?l.createElement(n,{is:a.is}):l.createElement(n)}}e[Re]=t,e[De]=a;e:for(l=t.child;null!==l;){if(5===l.tag||6===l.tag)e.appendChild(l.stateNode);else if(4!==l.tag&&27!==l.tag&&null!==l.child){l.child.return=l,l=l.child;continue}if(l===t)break e;for(;null===l.sibling;){if(null===l.return||l.return===t)break e;l=l.return}l.sibling.return=l.return,l=l.sibling}t.stateNode=e;e:switch(ad(e,n,a),n){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&to(t)}}return lo(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==a&&to(t);else{if("string"!=typeof a&&null===t.stateNode)throw Error(r(166));if(e=Z.current,ga(t)){if(e=t.stateNode,n=t.memoizedProps,a=null,null!==(l=ia))switch(l.tag){case 27:case 5:a=l.memoizedProps}e[Re]=t,(e=!!(e.nodeValue===n||null!==a&&!0===a.suppressHydrationWarning||ed(e.nodeValue,n)))||ma(t)}else(e=id(e).createTextNode(a))[Re]=t,t.stateNode=e}return lo(t),null;case 13:if(a=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(l=ga(t),null!==a&&null!==a.dehydrated){if(null===e){if(!l)throw Error(r(318));if(!(l=null!==(l=t.memoizedState)?l.dehydrated:null))throw Error(r(317));l[Re]=t}else ba(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;lo(t),l=!1}else l=va(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=l),l=!0;if(!l)return 256&t.flags?(fi(t),t):(fi(t),null)}if(fi(t),128&t.flags)return t.lanes=n,t;if(n=null!==a,e=null!==e&&null!==e.memoizedState,n){l=null,null!==(a=t.child).alternate&&null!==a.alternate.memoizedState&&null!==a.alternate.memoizedState.cachePool&&(l=a.alternate.memoizedState.cachePool.pool);var s=null;null!==a.memoizedState&&null!==a.memoizedState.cachePool&&(s=a.memoizedState.cachePool.pool),s!==l&&(a.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),ro(t,t.updateQueue),lo(t),null;case 4:return K(),null===e&&Hc(t.stateNode.containerInfo),lo(t),null;case 10:return Na(t.type),lo(t),null;case 19:if(H(mi),null===(l=t.memoizedState))return lo(t),null;if(a=!!(128&t.flags),null===(s=l.rendering))if(a)ao(l,!1);else{if(0!==gu||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(s=pi(e))){for(t.flags|=128,ao(l,!1),e=s.updateQueue,t.updateQueue=e,ro(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Br(n,e),n=n.sibling;return q(mi,1&mi.current|2),t.child}e=e.sibling}null!==l.tail&&le()>Cu&&(t.flags|=128,a=!0,ao(l,!1),t.lanes=4194304)}else{if(!a)if(null!==(e=pi(s))){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,ro(t,e),ao(l,!0),null===l.tail&&"hidden"===l.tailMode&&!s.alternate&&!ua)return lo(t),null}else 2*le()-l.renderingStartTime>Cu&&536870912!==n&&(t.flags|=128,a=!0,ao(l,!1),t.lanes=4194304);l.isBackwards?(s.sibling=t.child,t.child=s):(null!==(e=l.last)?e.sibling=s:t.child=s,l.last=s)}return null!==l.tail?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=le(),t.sibling=null,e=mi.current,q(mi,a?1&e|2:1&e),t):(lo(t),null);case 22:case 23:return fi(t),wl(),a=null!==t.memoizedState,null!==e?null!==e.memoizedState!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?!!(536870912&n)&&!(128&t.flags)&&(lo(t),6&t.subtreeFlags&&(t.flags|=8192)):lo(t),null!==(n=t.updateQueue)&&ro(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),a=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(a=t.memoizedState.cachePool.pool),a!==n&&(t.flags|=2048),null!==e&&H(qa),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),Na(Ra),lo(t),null;case 25:case 30:return null}throw Error(r(156,t.tag))}function io(e,t){switch(sa(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Na(Ra),K(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return J(t),null;case 13:if(fi(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(r(340));ba()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return H(mi),null;case 4:return K(),null;case 10:return Na(t.type),null;case 22:case 23:return fi(t),wl(),null!==e&&H(qa),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return Na(Ra),null;default:return null}}function oo(e,t){switch(sa(t),t.tag){case 3:Na(Ra),K();break;case 26:case 27:case 5:J(t);break;case 4:K();break;case 13:fi(t);break;case 19:H(mi);break;case 10:Na(t.type);break;case 22:case 23:fi(t),wl(),null!==e&&H(qa);break;case 24:Na(Ra)}}function uo(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var a=r.next;n=a;do{if((n.tag&e)===e){r=void 0;var l=n.create,s=n.inst;r=l(),s.destroy=r}n=n.next}while(n!==a)}}catch(i){pc(t,t.return,i)}}function co(e,t,n){try{var r=t.updateQueue,a=null!==r?r.lastEffect:null;if(null!==a){var l=a.next;r=l;do{if((r.tag&e)===e){var s=r.inst,i=s.destroy;if(void 0!==i){s.destroy=void 0,a=t;var o=n,u=i;try{u()}catch(c){pc(a,o,c)}}}r=r.next}while(r!==l)}}catch(c){pc(t,t.return,c)}}function fo(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{gl(t,n)}catch(r){pc(e,e.return,r)}}}function mo(e,t,n){n.props=yi(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){pc(e,t,r)}}function po(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"==typeof n?e.refCleanup=n(r):n.current=r}}catch(a){pc(e,t,a)}}function ho(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"==typeof r)try{r()}catch(a){pc(e,t,a)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"==typeof n)try{n(null)}catch(l){pc(e,t,l)}else n.current=null}function go(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(a){pc(e,e.return,a)}}function bo(e,t,n){try{var a=e.stateNode;!function(e,t,n,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var l=null,s=null,i=null,o=null,u=null,c=null,d=null;for(p in n){var f=n[p];if(n.hasOwnProperty(p)&&null!=f)switch(p){case"checked":case"value":break;case"defaultValue":u=f;default:a.hasOwnProperty(p)||nd(e,t,p,null,a,f)}}for(var m in a){var p=a[m];if(f=n[m],a.hasOwnProperty(m)&&(null!=p||null!=f))switch(m){case"type":s=p;break;case"name":l=p;break;case"checked":c=p;break;case"defaultChecked":d=p;break;case"value":i=p;break;case"defaultValue":o=p;break;case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(r(137,t));break;default:p!==f&&nd(e,t,m,p,a,f)}}return void xt(e,i,o,u,c,d,s,l);case"select":for(s in p=i=o=m=null,n)if(u=n[s],n.hasOwnProperty(s)&&null!=u)switch(s){case"value":break;case"multiple":p=u;default:a.hasOwnProperty(s)||nd(e,t,s,null,a,u)}for(l in a)if(s=a[l],u=n[l],a.hasOwnProperty(l)&&(null!=s||null!=u))switch(l){case"value":m=s;break;case"defaultValue":o=s;break;case"multiple":i=s;default:s!==u&&nd(e,t,l,s,a,u)}return t=o,n=i,a=p,void(null!=m?St(e,!!n,m,!1):!!a!=!!n&&(null!=t?St(e,!!n,t,!0):St(e,!!n,n?[]:"",!1)));case"textarea":for(o in p=m=null,n)if(l=n[o],n.hasOwnProperty(o)&&null!=l&&!a.hasOwnProperty(o))switch(o){case"value":case"children":break;default:nd(e,t,o,null,a,l)}for(i in a)if(l=a[i],s=n[i],a.hasOwnProperty(i)&&(null!=l||null!=s))switch(i){case"value":m=l;break;case"defaultValue":p=l;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=l)throw Error(r(91));break;default:l!==s&&nd(e,t,i,l,a,s)}return void Nt(e,m,p);case"option":for(var h in n)if(m=n[h],n.hasOwnProperty(h)&&null!=m&&!a.hasOwnProperty(h))if("selected"===h)e.selected=!1;else nd(e,t,h,null,a,m);for(u in a)if(m=a[u],p=n[u],a.hasOwnProperty(u)&&m!==p&&(null!=m||null!=p))if("selected"===u)e.selected=m&&"function"!=typeof m&&"symbol"!=typeof m;else nd(e,t,u,m,a,p);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)m=n[g],n.hasOwnProperty(g)&&null!=m&&!a.hasOwnProperty(g)&&nd(e,t,g,null,a,m);for(c in a)if(m=a[c],p=n[c],a.hasOwnProperty(c)&&m!==p&&(null!=m||null!=p))switch(c){case"children":case"dangerouslySetInnerHTML":if(null!=m)throw Error(r(137,t));break;default:nd(e,t,c,m,a,p)}return;default:if(zt(t)){for(var b in n)m=n[b],n.hasOwnProperty(b)&&void 0!==m&&!a.hasOwnProperty(b)&&rd(e,t,b,void 0,a,m);for(d in a)m=a[d],p=n[d],!a.hasOwnProperty(d)||m===p||void 0===m&&void 0===p||rd(e,t,d,m,a,p);return}}for(var v in n)m=n[v],n.hasOwnProperty(v)&&null!=m&&!a.hasOwnProperty(v)&&nd(e,t,v,null,a,m);for(f in a)m=a[f],p=n[f],!a.hasOwnProperty(f)||m===p||null==m&&null==p||nd(e,t,f,m,a,p)}(a,e.type,n,t),a[De]=t}catch(l){pc(e,e.return,l)}}function vo(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&bd(e.type)||4===e.tag}function yo(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||vo(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&bd(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function xo(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=td));else if(4!==r&&(27===r&&bd(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(xo(e,t,n),e=e.sibling;null!==e;)xo(e,t,n),e=e.sibling}function wo(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&bd(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(wo(e,t,n),e=e.sibling;null!==e;)wo(e,t,n),e=e.sibling}function ko(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,a=t.attributes;a.length;)t.removeAttributeNode(a[0]);ad(t,r,n),t[Re]=e,t[De]=n}catch(l){pc(e,e.return,l)}}var So=!1,No=!1,jo=!1,Co="function"==typeof WeakSet?WeakSet:Set,Eo=null;function To(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:Uo(e,n),4&r&&uo(5,n);break;case 1:if(Uo(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(s){pc(n,n.return,s)}else{var a=yi(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(a,t,e.__reactInternalSnapshotBeforeUpdate)}catch(i){pc(n,n.return,i)}}64&r&&fo(n),512&r&&po(n,n.return);break;case 3:if(Uo(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{gl(e,t)}catch(s){pc(n,n.return,s)}}break;case 27:null===t&&4&r&&ko(n);case 26:case 5:Uo(e,n),null===t&&4&r&&go(n),512&r&&po(n,n.return);break;case 12:Uo(e,n);break;case 13:Uo(e,n),4&r&&Mo(e,n),64&r&&(null!==(e=n.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=vc.bind(null,n))));break;case 22:if(!(r=null!==n.memoizedState||So)){t=null!==t&&null!==t.memoizedState||No,a=So;var l=No;So=r,(No=t)&&!l?Bo(e,n,!!(8772&n.subtreeFlags)):Uo(e,n),So=a,No=l}break;case 30:break;default:Uo(e,n)}}function Po(e){var t=e.alternate;null!==t&&(e.alternate=null,Po(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&He(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var zo=null,_o=!1;function Lo(e,t,n){for(n=n.child;null!==n;)Oo(e,t,n),n=n.sibling}function Oo(e,t,n){if(he&&"function"==typeof he.onCommitFiberUnmount)try{he.onCommitFiberUnmount(pe,n)}catch(l){}switch(n.tag){case 26:No||ho(n,t),Lo(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:No||ho(n,t);var r=zo,a=_o;bd(n.type)&&(zo=n.stateNode,_o=!1),Lo(e,t,n),jd(n.stateNode),zo=r,_o=a;break;case 5:No||ho(n,t);case 6:if(r=zo,a=_o,zo=null,Lo(e,t,n),_o=a,null!==(zo=r))if(_o)try{(9===zo.nodeType?zo.body:"HTML"===zo.nodeName?zo.ownerDocument.body:zo).removeChild(n.stateNode)}catch(s){pc(n,t,s)}else try{zo.removeChild(n.stateNode)}catch(s){pc(n,t,s)}break;case 18:null!==zo&&(_o?(vd(9===(e=zo).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Lf(e)):vd(zo,n.stateNode));break;case 4:r=zo,a=_o,zo=n.stateNode.containerInfo,_o=!0,Lo(e,t,n),zo=r,_o=a;break;case 0:case 11:case 14:case 15:No||co(2,n,t),No||co(4,n,t),Lo(e,t,n);break;case 1:No||(ho(n,t),"function"==typeof(r=n.stateNode).componentWillUnmount&&mo(n,t,r)),Lo(e,t,n);break;case 21:Lo(e,t,n);break;case 22:No=(r=No)||null!==n.memoizedState,Lo(e,t,n),No=r;break;default:Lo(e,t,n)}}function Mo(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{Lf(e)}catch(n){pc(t,t.return,n)}}function Fo(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new Co),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new Co),t;default:throw Error(r(435,e.tag))}}(e);t.forEach((function(t){var r=yc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}function Ro(e,t){var n=t.deletions;if(null!==n)for(var a=0;a<n.length;a++){var l=n[a],s=e,i=t,o=i;e:for(;null!==o;){switch(o.tag){case 27:if(bd(o.type)){zo=o.stateNode,_o=!1;break e}break;case 5:zo=o.stateNode,_o=!1;break e;case 3:case 4:zo=o.stateNode.containerInfo,_o=!0;break e}o=o.return}if(null===zo)throw Error(r(160));Oo(s,i,l),zo=null,_o=!1,null!==(s=l.alternate)&&(s.return=null),l.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)Ao(t,e),t=t.sibling}var Do=null;function Ao(e,t){var n=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Ro(t,e),Io(e),4&a&&(co(3,e,e.return),uo(3,e),co(5,e,e.return));break;case 1:Ro(t,e),Io(e),512&a&&(No||null===n||ho(n,n.return)),64&a&&So&&(null!==(e=e.updateQueue)&&(null!==(a=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?a:n.concat(a))));break;case 26:var l=Do;if(Ro(t,e),Io(e),512&a&&(No||null===n||ho(n,n.return)),4&a){var s=null!==n?n.memoizedState:null;if(a=e.memoizedState,null===n)if(null===a)if(null===e.stateNode){e:{a=e.type,n=e.memoizedProps,l=l.ownerDocument||l;t:switch(a){case"title":(!(s=l.getElementsByTagName("title")[0])||s[Be]||s[Re]||"http://www.w3.org/2000/svg"===s.namespaceURI||s.hasAttribute("itemprop"))&&(s=l.createElement(a),l.head.insertBefore(s,l.querySelector("head > title"))),ad(s,a,n),s[Re]=e,Ye(s),a=s;break e;case"link":var i=Bd("link","href",l).get(a+(n.href||""));if(i)for(var o=0;o<i.length;o++)if((s=i[o]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&s.getAttribute("rel")===(null==n.rel?null:n.rel)&&s.getAttribute("title")===(null==n.title?null:n.title)&&s.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){i.splice(o,1);break t}ad(s=l.createElement(a),a,n),l.head.appendChild(s);break;case"meta":if(i=Bd("meta","content",l).get(a+(n.content||"")))for(o=0;o<i.length;o++)if((s=i[o]).getAttribute("content")===(null==n.content?null:""+n.content)&&s.getAttribute("name")===(null==n.name?null:n.name)&&s.getAttribute("property")===(null==n.property?null:n.property)&&s.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&s.getAttribute("charset")===(null==n.charSet?null:n.charSet)){i.splice(o,1);break t}ad(s=l.createElement(a),a,n),l.head.appendChild(s);break;default:throw Error(r(468,a))}s[Re]=e,Ye(s),a=s}e.stateNode=a}else Hd(l,e.type,e.stateNode);else e.stateNode=Ad(l,a,e.memoizedProps);else s!==a?(null===s?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):s.count--,null===a?Hd(l,e.type,e.stateNode):Ad(l,a,e.memoizedProps)):null===a&&null!==e.stateNode&&bo(e,e.memoizedProps,n.memoizedProps)}break;case 27:Ro(t,e),Io(e),512&a&&(No||null===n||ho(n,n.return)),null!==n&&4&a&&bo(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Ro(t,e),Io(e),512&a&&(No||null===n||ho(n,n.return)),32&e.flags){l=e.stateNode;try{Ct(l,"")}catch(p){pc(e,e.return,p)}}4&a&&null!=e.stateNode&&bo(e,l=e.memoizedProps,null!==n?n.memoizedProps:l),1024&a&&(jo=!0);break;case 6:if(Ro(t,e),Io(e),4&a){if(null===e.stateNode)throw Error(r(162));a=e.memoizedProps,n=e.stateNode;try{n.nodeValue=a}catch(p){pc(e,e.return,p)}}break;case 3:if(Vd=null,l=Do,Do=Td(t.containerInfo),Ro(t,e),Do=l,Io(e),4&a&&null!==n&&n.memoizedState.isDehydrated)try{Lf(t.containerInfo)}catch(p){pc(e,e.return,p)}jo&&(jo=!1,$o(e));break;case 4:a=Do,Do=Td(e.stateNode.containerInfo),Ro(t,e),Io(e),Do=a;break;case 12:default:Ro(t,e),Io(e);break;case 13:Ro(t,e),Io(e),8192&e.child.flags&&null!==e.memoizedState!=(null!==n&&null!==n.memoizedState)&&(ju=le()),4&a&&(null!==(a=e.updateQueue)&&(e.updateQueue=null,Fo(e,a)));break;case 22:l=null!==e.memoizedState;var u=null!==n&&null!==n.memoizedState,c=So,d=No;if(So=c||l,No=d||u,Ro(t,e),No=d,So=c,Io(e),8192&a)e:for(t=e.stateNode,t._visibility=l?-2&t._visibility:1|t._visibility,l&&(null===n||u||So||No||Vo(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){u=n=t;try{if(s=u.stateNode,l)"function"==typeof(i=s.style).setProperty?i.setProperty("display","none","important"):i.display="none";else{o=u.stateNode;var f=u.memoizedProps.style,m=null!=f&&f.hasOwnProperty("display")?f.display:null;o.style.display=null==m||"boolean"==typeof m?"":(""+m).trim()}}catch(p){pc(u,u.return,p)}}}else if(6===t.tag){if(null===n){u=t;try{u.stateNode.nodeValue=l?"":u.memoizedProps}catch(p){pc(u,u.return,p)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&a&&(null!==(a=e.updateQueue)&&(null!==(n=a.retryQueue)&&(a.retryQueue=null,Fo(e,n))));break;case 19:Ro(t,e),Io(e),4&a&&(null!==(a=e.updateQueue)&&(e.updateQueue=null,Fo(e,a)));case 30:case 21:}}function Io(e){var t=e.flags;if(2&t){try{for(var n,a=e.return;null!==a;){if(vo(a)){n=a;break}a=a.return}if(null==n)throw Error(r(160));switch(n.tag){case 27:var l=n.stateNode;wo(e,yo(e),l);break;case 5:var s=n.stateNode;32&n.flags&&(Ct(s,""),n.flags&=-33),wo(e,yo(e),s);break;case 3:case 4:var i=n.stateNode.containerInfo;xo(e,yo(e),i);break;default:throw Error(r(161))}}catch(o){pc(e,e.return,o)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function $o(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;$o(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function Uo(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)To(e,t.alternate,t),t=t.sibling}function Vo(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:co(4,t,t.return),Vo(t);break;case 1:ho(t,t.return);var n=t.stateNode;"function"==typeof n.componentWillUnmount&&mo(t,t.return,n),Vo(t);break;case 27:jd(t.stateNode);case 26:case 5:ho(t,t.return),Vo(t);break;case 22:null===t.memoizedState&&Vo(t);break;default:Vo(t)}e=e.sibling}}function Bo(e,t,n){for(n=n&&!!(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,a=e,l=t,s=l.flags;switch(l.tag){case 0:case 11:case 15:Bo(a,l,n),uo(4,l);break;case 1:if(Bo(a,l,n),"function"==typeof(a=(r=l).stateNode).componentDidMount)try{a.componentDidMount()}catch(u){pc(r,r.return,u)}if(null!==(a=(r=l).updateQueue)){var i=r.stateNode;try{var o=a.shared.hiddenCallbacks;if(null!==o)for(a.shared.hiddenCallbacks=null,a=0;a<o.length;a++)hl(o[a],i)}catch(u){pc(r,r.return,u)}}n&&64&s&&fo(l),po(l,l.return);break;case 27:ko(l);case 26:case 5:Bo(a,l,n),n&&null===r&&4&s&&go(l),po(l,l.return);break;case 12:Bo(a,l,n);break;case 13:Bo(a,l,n),n&&4&s&&Mo(a,l);break;case 22:null===l.memoizedState&&Bo(a,l,n),po(l,l.return);break;case 30:break;default:Bo(a,l,n)}t=t.sibling}}function Ho(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&Aa(n))}function qo(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Aa(e))}function Wo(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Qo(e,t,n,r),t=t.sibling}function Qo(e,t,n,r){var a=t.flags;switch(t.tag){case 0:case 11:case 15:Wo(e,t,n,r),2048&a&&uo(9,t);break;case 1:case 13:default:Wo(e,t,n,r);break;case 3:Wo(e,t,n,r),2048&a&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Aa(e)));break;case 12:if(2048&a){Wo(e,t,n,r),e=t.stateNode;try{var l=t.memoizedProps,s=l.id,i=l.onPostCommit;"function"==typeof i&&i(s,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(o){pc(t,t.return,o)}}else Wo(e,t,n,r);break;case 23:break;case 22:l=t.stateNode,s=t.alternate,null!==t.memoizedState?2&l._visibility?Wo(e,t,n,r):Yo(e,t):2&l._visibility?Wo(e,t,n,r):(l._visibility|=2,Zo(e,t,n,r,!!(10256&t.subtreeFlags))),2048&a&&Ho(s,t);break;case 24:Wo(e,t,n,r),2048&a&&qo(t.alternate,t)}}function Zo(e,t,n,r,a){for(a=a&&!!(10256&t.subtreeFlags),t=t.child;null!==t;){var l=e,s=t,i=n,o=r,u=s.flags;switch(s.tag){case 0:case 11:case 15:Zo(l,s,i,o,a),uo(8,s);break;case 23:break;case 22:var c=s.stateNode;null!==s.memoizedState?2&c._visibility?Zo(l,s,i,o,a):Yo(l,s):(c._visibility|=2,Zo(l,s,i,o,a)),a&&2048&u&&Ho(s.alternate,s);break;case 24:Zo(l,s,i,o,a),a&&2048&u&&qo(s.alternate,s);break;default:Zo(l,s,i,o,a)}t=t.sibling}}function Yo(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,a=r.flags;switch(r.tag){case 22:Yo(n,r),2048&a&&Ho(r.alternate,r);break;case 24:Yo(n,r),2048&a&&qo(r.alternate,r);break;default:Yo(n,r)}t=t.sibling}}var Go=8192;function Ko(e){if(e.subtreeFlags&Go)for(e=e.child;null!==e;)Xo(e),e=e.sibling}function Xo(e){switch(e.tag){case 26:Ko(e),e.flags&Go&&null!==e.memoizedState&&function(e,t,n){if(null===Wd)throw Error(r(475));var a=Wd;if(!("stylesheet"!==t.type||"string"==typeof n.media&&!1===matchMedia(n.media).matches||4&t.state.loading)){if(null===t.instance){var l=Od(n.href),s=e.querySelector(Md(l));if(s)return null!==(e=s._p)&&"object"==typeof e&&"function"==typeof e.then&&(a.count++,a=Zd.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=s,void Ye(s);s=e.ownerDocument||e,n=Fd(n),(l=Cd.get(l))&&$d(n,l),Ye(s=s.createElement("link"));var i=s;i._p=new Promise((function(e,t){i.onload=e,i.onerror=t})),ad(s,"link",n),t.instance=s}null===a.stylesheets&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&!(3&t.state.loading)&&(a.count++,t=Zd.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}(Do,e.memoizedState,e.memoizedProps);break;case 5:default:Ko(e);break;case 3:case 4:var t=Do;Do=Td(e.stateNode.containerInfo),Ko(e),Do=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=Go,Go=16777216,Ko(e),Go=t):Ko(e))}}function Jo(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function eu(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Eo=r,ru(r,e)}Jo(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)tu(e),e=e.sibling}function tu(e){switch(e.tag){case 0:case 11:case 15:eu(e),2048&e.flags&&co(9,e,e.return);break;case 3:case 12:default:eu(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,nu(e)):eu(e)}}function nu(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Eo=r,ru(r,e)}Jo(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:co(8,t,t.return),nu(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,nu(t));break;default:nu(t)}e=e.sibling}}function ru(e,t){for(;null!==Eo;){var n=Eo;switch(n.tag){case 0:case 11:case 15:co(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:Aa(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,Eo=r;else e:for(n=e;null!==Eo;){var a=(r=Eo).sibling,l=r.return;if(Po(r),r===n){Eo=null;break e}if(null!==a){a.return=l,Eo=a;break e}Eo=l}}}var au={getCacheForType:function(e){var t=za(Ra),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},lu="function"==typeof WeakMap?WeakMap:Map,su=0,iu=null,ou=null,uu=0,cu=0,du=null,fu=!1,mu=!1,pu=!1,hu=0,gu=0,bu=0,vu=0,yu=0,xu=0,wu=0,ku=null,Su=null,Nu=!1,ju=0,Cu=1/0,Eu=null,Tu=null,Pu=0,zu=null,_u=null,Lu=0,Ou=0,Mu=null,Fu=null,Ru=0,Du=null;function Au(){if(2&su&&0!==uu)return uu&-uu;if(null!==A.T){return 0!==Ua?Ua:Oc()}return Me()}function Iu(){0===xu&&(xu=536870912&uu&&!ua?536870912:Ce());var e=ii.current;return null!==e&&(e.flags|=32),xu}function $u(e,t,n){(e!==iu||2!==cu&&9!==cu)&&null===e.cancelPendingCommit||(Qu(e,0),Hu(e,uu,xu,!1)),Pe(e,n),2&su&&e===iu||(e===iu&&(!(2&su)&&(vu|=n),4===gu&&Hu(e,uu,xu,!1)),Cc(e))}function Uu(e,t,n){if(6&su)throw Error(r(327));for(var a=!n&&!(124&t)&&0===(t&e.expiredLanes)||Ne(e,t),l=a?function(e,t){var n=su;su|=2;var a=Yu(),l=Gu();iu!==e||uu!==t?(Eu=null,Cu=le()+500,Qu(e,t)):mu=Ne(e,t);e:for(;;)try{if(0!==cu&&null!==ou){t=ou;var s=du;t:switch(cu){case 1:cu=0,du=null,rc(e,t,s,1);break;case 2:case 9:if(Ja(s)){cu=0,du=null,nc(t);break}t=function(){2!==cu&&9!==cu||iu!==e||(cu=7),Cc(e)},s.then(t,t);break e;case 3:cu=7;break e;case 4:cu=5;break e;case 7:Ja(s)?(cu=0,du=null,nc(t)):(cu=0,du=null,rc(e,t,s,7));break;case 5:var i=null;switch(ou.tag){case 26:i=ou.memoizedState;case 5:case 27:var o=ou;if(!i||qd(i)){cu=0,du=null;var u=o.sibling;if(null!==u)ou=u;else{var c=o.return;null!==c?(ou=c,ac(c)):ou=null}break t}}cu=0,du=null,rc(e,t,s,5);break;case 6:cu=0,du=null,rc(e,t,s,6);break;case 8:Wu(),gu=6;break e;default:throw Error(r(462))}}ec();break}catch(d){Zu(e,d)}return ka=wa=null,A.H=a,A.A=l,su=n,null!==ou?0:(iu=null,uu=0,Lr(),gu)}(e,t):Xu(e,t,!0),s=a;;){if(0===l){mu&&!a&&Hu(e,t,0,!1);break}if(n=e.current.alternate,!s||Bu(n)){if(2===l){if(s=t,e.errorRecoveryDisabledLanes&s)var i=0;else i=0!==(i=-536870913&e.pendingLanes)?i:536870912&i?536870912:0;if(0!==i){t=i;e:{var o=e;l=ku;var u=o.current.memoizedState.isDehydrated;if(u&&(Qu(o,i).flags|=256),2!==(i=Xu(o,i,!1))){if(pu&&!u){o.errorRecoveryDisabledLanes|=s,vu|=s,l=4;break e}s=Su,Su=l,null!==s&&(null===Su?Su=s:Su.push.apply(Su,s))}l=i}if(s=!1,2!==l)continue}}if(1===l){Qu(e,0),Hu(e,t,0,!0);break}e:{switch(a=e,s=l){case 0:case 1:throw Error(r(345));case 4:if((4194048&t)!==t)break;case 6:Hu(a,t,xu,!fu);break e;case 2:Su=null;break;case 3:case 5:break;default:throw Error(r(329))}if((62914560&t)===t&&10<(l=ju+300-le())){if(Hu(a,t,xu,!fu),0!==Se(a,0,!0))break e;a.timeoutHandle=fd(Vu.bind(null,a,n,Su,Eu,Nu,t,xu,vu,wu,fu,s,2,-0,0),l)}else Vu(a,n,Su,Eu,Nu,t,xu,vu,wu,fu,s,0,-0,0)}break}l=Xu(e,t,!1),s=!1}Cc(e)}function Vu(e,t,n,a,l,s,i,o,u,c,d,f,m,p){if(e.timeoutHandle=-1,(8192&(f=t.subtreeFlags)||!(16785408&~f))&&(Wd={stylesheets:null,count:0,unsuspend:Qd},Xo(t),null!==(f=function(){if(null===Wd)throw Error(r(475));var e=Wd;return e.stylesheets&&0===e.count&&Gd(e,e.stylesheets),0<e.count?function(t){var n=setTimeout((function(){if(e.stylesheets&&Gd(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}}),6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=f(sc.bind(null,e,t,s,n,a,l,i,o,u,d,1,m,p)),void Hu(e,s,i,!c);sc(e,t,s,n,a,l,i,o,u)}function Bu(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var r=0;r<n.length;r++){var a=n[r],l=a.getSnapshot;a=a.value;try{if(!Jn(l(),a))return!1}catch(s){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Hu(e,t,n,r){t&=~yu,t&=~vu,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var a=t;0<a;){var l=31-be(a),s=1<<l;r[l]=-1,a&=~s}0!==n&&ze(e,n,t)}function qu(){return!!(6&su)||(Ec(0),!1)}function Wu(){if(null!==ou){if(0===cu)var e=ou.return;else ka=wa=null,Ul(e=ou),Xs=null,Js=0,e=ou;for(;null!==e;)oo(e.alternate,e),e=e.return;ou=null}}function Qu(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,md(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Wu(),iu=e,ou=n=Vr(e.current,null),uu=t,cu=0,du=null,fu=!1,mu=Ne(e,t),pu=!1,wu=xu=yu=vu=bu=gu=0,Su=ku=null,Nu=!1,8&t&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var a=31-be(r),l=1<<a;t|=e[a],r&=~l}return hu=t,Lr(),n}function Zu(e,t){Sl=null,A.H=Zs,t===Ya||t===Ka?(t=rl(),cu=3):t===Ga?(t=rl(),cu=4):cu=t===Pi?8:null!==t&&"object"==typeof t&&"function"==typeof t.then?6:1,du=t,null===ou&&(gu=1,Ni(e,Tr(t,e.current)))}function Yu(){var e=A.H;return A.H=Zs,null===e?Zs:e}function Gu(){var e=A.A;return A.A=au,e}function Ku(){gu=4,fu||(4194048&uu)!==uu&&null!==ii.current||(mu=!0),!(134217727&bu)&&!(134217727&vu)||null===iu||Hu(iu,uu,xu,!1)}function Xu(e,t,n){var r=su;su|=2;var a=Yu(),l=Gu();iu===e&&uu===t||(Eu=null,Qu(e,t)),t=!1;var s=gu;e:for(;;)try{if(0!==cu&&null!==ou){var i=ou,o=du;switch(cu){case 8:Wu(),s=6;break e;case 3:case 2:case 9:case 6:null===ii.current&&(t=!0);var u=cu;if(cu=0,du=null,rc(e,i,o,u),n&&mu){s=0;break e}break;default:u=cu,cu=0,du=null,rc(e,i,o,u)}}Ju(),s=gu;break}catch(c){Zu(e,c)}return t&&e.shellSuspendCounter++,ka=wa=null,su=r,A.H=a,A.A=l,null===ou&&(iu=null,uu=0,Lr()),s}function Ju(){for(;null!==ou;)tc(ou)}function ec(){for(;null!==ou&&!re();)tc(ou)}function tc(e){var t=eo(e.alternate,e,hu);e.memoizedProps=e.pendingProps,null===t?ac(e):ou=t}function nc(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Ii(n,t,t.pendingProps,t.type,void 0,uu);break;case 11:t=Ii(n,t,t.pendingProps,t.type.render,t.ref,uu);break;case 5:Ul(t);default:oo(n,t),t=eo(n,t=ou=Br(t,hu),hu)}e.memoizedProps=e.pendingProps,null===t?ac(e):ou=t}function rc(e,t,n,a){ka=wa=null,Ul(t),Xs=null,Js=0;var l=t.return;try{if(function(e,t,n,a,l){if(n.flags|=32768,null!==a&&"object"==typeof a&&"function"==typeof a.then){if(null!==(t=n.alternate)&&Ea(t,n,l,!0),null!==(n=ii.current)){switch(n.tag){case 13:return null===oi?Ku():null===n.alternate&&0===gu&&(gu=3),n.flags&=-257,n.flags|=65536,n.lanes=l,a===Xa?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([a]):t.add(a),hc(e,a,l)),!1;case 22:return n.flags|=65536,a===Xa?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([a]):n.add(a),hc(e,a,l)),!1}throw Error(r(435,n.tag))}return hc(e,a,l),Ku(),!1}if(ua)return null!==(t=ii.current)?(!(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=l,a!==fa&&ya(Tr(e=Error(r(422),{cause:a}),n))):(a!==fa&&ya(Tr(t=Error(r(423),{cause:a}),n)),(e=e.current.alternate).flags|=65536,l&=-l,e.lanes|=l,a=Tr(a,n),dl(e,l=Ci(e.stateNode,a,l)),4!==gu&&(gu=2)),!1;var s=Error(r(520),{cause:a});if(s=Tr(s,n),null===ku?ku=[s]:ku.push(s),4!==gu&&(gu=2),null===t)return!0;a=Tr(a,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=l&-l,n.lanes|=e,dl(n,e=Ci(n.stateNode,a,e)),!1;case 1:if(t=n.type,s=n.stateNode,!(128&n.flags||"function"!=typeof t.getDerivedStateFromError&&(null===s||"function"!=typeof s.componentDidCatch||null!==Tu&&Tu.has(s))))return n.flags|=65536,l&=-l,n.lanes|=l,Ti(l=Ei(l),e,n,a),dl(n,l),!1}n=n.return}while(null!==n);return!1}(e,l,t,n,uu))return gu=1,Ni(e,Tr(n,e.current)),void(ou=null)}catch(s){if(null!==l)throw ou=l,s;return gu=1,Ni(e,Tr(n,e.current)),void(ou=null)}32768&t.flags?(ua||1===a?e=!0:mu||536870912&uu?e=!1:(fu=e=!0,(2===a||9===a||3===a||6===a)&&(null!==(a=ii.current)&&13===a.tag&&(a.flags|=16384))),lc(t,e)):ac(t)}function ac(e){var t=e;do{if(32768&t.flags)return void lc(t,fu);e=t.return;var n=so(t.alternate,t,hu);if(null!==n)return void(ou=n);if(null!==(t=t.sibling))return void(ou=t);ou=t=e}while(null!==t);0===gu&&(gu=5)}function lc(e,t){do{var n=io(e.alternate,e);if(null!==n)return n.flags&=32767,void(ou=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(ou=e);ou=e=n}while(null!==e);gu=6,ou=null}function sc(e,t,n,a,l,s,i,o,u){e.cancelPendingCommit=null;do{dc()}while(0!==Pu);if(6&su)throw Error(r(327));if(null!==t){if(t===e.current)throw Error(r(177));if(s=t.lanes|t.childLanes,function(e,t,n,r,a,l){var s=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var i=e.entanglements,o=e.expirationTimes,u=e.hiddenUpdates;for(n=s&~n;0<n;){var c=31-be(n),d=1<<c;i[c]=0,o[c]=-1;var f=u[c];if(null!==f)for(u[c]=null,c=0;c<f.length;c++){var m=f[c];null!==m&&(m.lane&=-536870913)}n&=~d}0!==r&&ze(e,r,0),0!==l&&0===a&&0!==e.tag&&(e.suspendedLanes|=l&~(s&~t))}(e,n,s|=_r,i,o,u),e===iu&&(ou=iu=null,uu=0),_u=t,zu=e,Lu=n,Ou=s,Mu=l,Fu=a,10256&t.subtreeFlags||10256&t.flags?(e.callbackNode=null,e.callbackPriority=0,te(ue,(function(){return fc(),null}))):(e.callbackNode=null,e.callbackPriority=0),a=!!(13878&t.flags),13878&t.subtreeFlags||a){a=A.T,A.T=null,l=I.p,I.p=2,i=su,su|=4;try{!function(e,t){if(e=e.containerInfo,ld=sf,lr(e=ar(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var a=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(a&&0!==a.rangeCount){n=a.anchorNode;var l=a.anchorOffset,s=a.focusNode;a=a.focusOffset;try{n.nodeType,s.nodeType}catch(g){n=null;break e}var i=0,o=-1,u=-1,c=0,d=0,f=e,m=null;t:for(;;){for(var p;f!==n||0!==l&&3!==f.nodeType||(o=i+l),f!==s||0!==a&&3!==f.nodeType||(u=i+a),3===f.nodeType&&(i+=f.nodeValue.length),null!==(p=f.firstChild);)m=f,f=p;for(;;){if(f===e)break t;if(m===n&&++c===l&&(o=i),m===s&&++d===a&&(u=i),null!==(p=f.nextSibling))break;m=(f=m).parentNode}f=p}n=-1===o||-1===u?null:{start:o,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(sd={focusedElem:e,selectionRange:n},sf=!1,Eo=t;null!==Eo;)if(e=(t=Eo).child,1024&t.subtreeFlags&&null!==e)e.return=t,Eo=e;else for(;null!==Eo;){switch(s=(t=Eo).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(1024&e&&null!==s){e=void 0,n=t,l=s.memoizedProps,s=s.memoizedState,a=n.stateNode;try{var h=yi(n.type,l,(n.elementType,n.type));e=a.getSnapshotBeforeUpdate(h,s),a.__reactInternalSnapshotBeforeUpdate=e}catch(b){pc(n,n.return,b)}}break;case 3:if(1024&e)if(9===(n=(e=t.stateNode.containerInfo).nodeType))yd(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":yd(e);break;default:e.textContent=""}break;default:if(1024&e)throw Error(r(163))}if(null!==(e=t.sibling)){e.return=t.return,Eo=e;break}Eo=t.return}}(e,t)}finally{su=i,I.p=l,A.T=a}}Pu=1,ic(),oc(),uc()}}function ic(){if(1===Pu){Pu=0;var e=zu,t=_u,n=!!(13878&t.flags);if(13878&t.subtreeFlags||n){n=A.T,A.T=null;var r=I.p;I.p=2;var a=su;su|=4;try{Ao(t,e);var l=sd,s=ar(e.containerInfo),i=l.focusedElem,o=l.selectionRange;if(s!==i&&i&&i.ownerDocument&&rr(i.ownerDocument.documentElement,i)){if(null!==o&&lr(i)){var u=o.start,c=o.end;if(void 0===c&&(c=u),"selectionStart"in i)i.selectionStart=u,i.selectionEnd=Math.min(c,i.value.length);else{var d=i.ownerDocument||document,f=d&&d.defaultView||window;if(f.getSelection){var m=f.getSelection(),p=i.textContent.length,h=Math.min(o.start,p),g=void 0===o.end?h:Math.min(o.end,p);!m.extend&&h>g&&(s=g,g=h,h=s);var b=nr(i,h),v=nr(i,g);if(b&&v&&(1!==m.rangeCount||m.anchorNode!==b.node||m.anchorOffset!==b.offset||m.focusNode!==v.node||m.focusOffset!==v.offset)){var y=d.createRange();y.setStart(b.node,b.offset),m.removeAllRanges(),h>g?(m.addRange(y),m.extend(v.node,v.offset)):(y.setEnd(v.node,v.offset),m.addRange(y))}}}}for(d=[],m=i;m=m.parentNode;)1===m.nodeType&&d.push({element:m,left:m.scrollLeft,top:m.scrollTop});for("function"==typeof i.focus&&i.focus(),i=0;i<d.length;i++){var x=d[i];x.element.scrollLeft=x.left,x.element.scrollTop=x.top}}sf=!!ld,sd=ld=null}finally{su=a,I.p=r,A.T=n}}e.current=t,Pu=2}}function oc(){if(2===Pu){Pu=0;var e=zu,t=_u,n=!!(8772&t.flags);if(8772&t.subtreeFlags||n){n=A.T,A.T=null;var r=I.p;I.p=2;var a=su;su|=4;try{To(e,t.alternate,t)}finally{su=a,I.p=r,A.T=n}}Pu=3}}function uc(){if(4===Pu||3===Pu){Pu=0,ae();var e=zu,t=_u,n=Lu,r=Fu;10256&t.subtreeFlags||10256&t.flags?Pu=5:(Pu=0,_u=zu=null,cc(e,e.pendingLanes));var a=e.pendingLanes;if(0===a&&(Tu=null),Oe(n),t=t.stateNode,he&&"function"==typeof he.onCommitFiberRoot)try{he.onCommitFiberRoot(pe,t,void 0,!(128&~t.current.flags))}catch(o){}if(null!==r){t=A.T,a=I.p,I.p=2,A.T=null;try{for(var l=e.onRecoverableError,s=0;s<r.length;s++){var i=r[s];l(i.value,{componentStack:i.stack})}}finally{A.T=t,I.p=a}}3&Lu&&dc(),Cc(e),a=e.pendingLanes,4194090&n&&42&a?e===Du?Ru++:(Ru=0,Du=e):Ru=0,Ec(0)}}function cc(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,Aa(t)))}function dc(e){return ic(),oc(),uc(),fc()}function fc(){if(5!==Pu)return!1;var e=zu,t=Ou;Ou=0;var n=Oe(Lu),a=A.T,l=I.p;try{I.p=32>n?32:n,A.T=null,n=Mu,Mu=null;var s=zu,i=Lu;if(Pu=0,_u=zu=null,Lu=0,6&su)throw Error(r(331));var o=su;if(su|=4,tu(s.current),Qo(s,s.current,i,n),su=o,Ec(0,!1),he&&"function"==typeof he.onPostCommitFiberRoot)try{he.onPostCommitFiberRoot(pe,s)}catch(u){}return!0}finally{I.p=l,A.T=a,cc(e,t)}}function mc(e,t,n){t=Tr(n,t),null!==(e=ul(e,t=Ci(e.stateNode,t,2),2))&&(Pe(e,2),Cc(e))}function pc(e,t,n){if(3===e.tag)mc(e,e,n);else for(;null!==t;){if(3===t.tag){mc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===Tu||!Tu.has(r))){e=Tr(n,e),null!==(r=ul(t,n=Ei(2),2))&&(Ti(n,r,t,e),Pe(r,2),Cc(r));break}}t=t.return}}function hc(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new lu;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(pu=!0,a.add(n),e=gc.bind(null,e,t,n),t.then(e,e))}function gc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,iu===e&&(uu&n)===n&&(4===gu||3===gu&&(62914560&uu)===uu&&300>le()-ju?!(2&su)&&Qu(e,0):yu|=n,wu===uu&&(wu=0)),Cc(e)}function bc(e,t){0===t&&(t=Ee()),null!==(e=Fr(e,t))&&(Pe(e,t),Cc(e))}function vc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),bc(e,n)}function yc(e,t){var n=0;switch(e.tag){case 13:var a=e.stateNode,l=e.memoizedState;null!==l&&(n=l.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(r(314))}null!==a&&a.delete(t),bc(e,n)}var xc=null,wc=null,kc=!1,Sc=!1,Nc=!1,jc=0;function Cc(e){e!==wc&&null===e.next&&(null===wc?xc=wc=e:wc=wc.next=e),Sc=!0,kc||(kc=!0,hd((function(){6&su?te(ie,Tc):Pc()})))}function Ec(e,t){if(!Nc&&Sc){Nc=!0;do{for(var n=!1,r=xc;null!==r;){if(0!==e){var a=r.pendingLanes;if(0===a)var l=0;else{var s=r.suspendedLanes,i=r.pingedLanes;l=(1<<31-be(42|e)+1)-1,l=201326741&(l&=a&~(s&~i))?201326741&l|1:l?2|l:0}0!==l&&(n=!0,Lc(r,l))}else l=uu,!(3&(l=Se(r,r===iu?l:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||Ne(r,l)||(n=!0,Lc(r,l));r=r.next}}while(n);Nc=!1}}function Tc(){Pc()}function Pc(){Sc=kc=!1;var e=0;0!==jc&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==dd&&(dd=e,!0);return dd=null,!1}()&&(e=jc),jc=0);for(var t=le(),n=null,r=xc;null!==r;){var a=r.next,l=zc(r,t);0===l?(r.next=null,null===n?xc=a:n.next=a,null===a&&(wc=n)):(n=r,(0!==e||3&l)&&(Sc=!0)),r=a}Ec(e)}function zc(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,l=-62914561&e.pendingLanes;0<l;){var s=31-be(l),i=1<<s,o=a[s];-1===o?0!==(i&n)&&0===(i&r)||(a[s]=je(i,t)):o<=t&&(e.expiredLanes|=i),l&=~i}if(n=uu,n=Se(e,e===(t=iu)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===cu||9===cu)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&ne(r),e.callbackNode=null,e.callbackPriority=0;if(!(3&n)||Ne(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&ne(r),Oe(n)){case 2:case 8:n=oe;break;case 32:default:n=ue;break;case 268435456:n=de}return r=_c.bind(null,e),n=te(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&ne(r),e.callbackPriority=2,e.callbackNode=null,2}function _c(e,t){if(0!==Pu&&5!==Pu)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(dc()&&e.callbackNode!==n)return null;var r=uu;return 0===(r=Se(e,e===iu?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Uu(e,r,t),zc(e,le()),null!=e.callbackNode&&e.callbackNode===n?_c.bind(null,e):null)}function Lc(e,t){if(dc())return null;Uu(e,t,!0)}function Oc(){return 0===jc&&(jc=Ce()),jc}function Mc(e){return null==e||"symbol"==typeof e||"boolean"==typeof e?null:"function"==typeof e?e:Ot(""+e)}function Fc(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var Rc=0;Rc<jr.length;Rc++){var Dc=jr[Rc];Cr(Dc.toLowerCase(),"on"+(Dc[0].toUpperCase()+Dc.slice(1)))}Cr(br,"onAnimationEnd"),Cr(vr,"onAnimationIteration"),Cr(yr,"onAnimationStart"),Cr("dblclick","onDoubleClick"),Cr("focusin","onFocus"),Cr("focusout","onBlur"),Cr(xr,"onTransitionRun"),Cr(wr,"onTransitionStart"),Cr(kr,"onTransitionCancel"),Cr(Sr,"onTransitionEnd"),Je("onMouseEnter",["mouseout","mouseover"]),Je("onMouseLeave",["mouseout","mouseover"]),Je("onPointerEnter",["pointerout","pointerover"]),Je("onPointerLeave",["pointerout","pointerover"]),Xe("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Xe("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Xe("onBeforeInput",["compositionend","keypress","textInput","paste"]),Xe("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Xe("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Xe("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ac="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ic=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ac));function $c(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var s=r.length-1;0<=s;s--){var i=r[s],o=i.instance,u=i.currentTarget;if(i=i.listener,o!==l&&a.isPropagationStopped())break e;l=i,a.currentTarget=u;try{l(a)}catch(c){xi(c)}a.currentTarget=null,l=o}else for(s=0;s<r.length;s++){if(o=(i=r[s]).instance,u=i.currentTarget,i=i.listener,o!==l&&a.isPropagationStopped())break e;l=i,a.currentTarget=u;try{l(a)}catch(c){xi(c)}a.currentTarget=null,l=o}}}}function Uc(e,t){var n=t[Ie];void 0===n&&(n=t[Ie]=new Set);var r=e+"__bubble";n.has(r)||(qc(t,e,2,!1),n.add(r))}function Vc(e,t,n){var r=0;t&&(r|=4),qc(n,e,r,t)}var Bc="_reactListening"+Math.random().toString(36).slice(2);function Hc(e){if(!e[Bc]){e[Bc]=!0,Ge.forEach((function(t){"selectionchange"!==t&&(Ic.has(t)||Vc(t,!1,e),Vc(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Bc]||(t[Bc]=!0,Vc("selectionchange",!1,t))}}function qc(e,t,n,r){switch(pf(t)){case 2:var a=of;break;case 8:a=uf;break;default:a=cf}n=a.bind(null,t,n,e),a=void 0,!Bt||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Wc(e,t,n,r,a){var s=r;if(!(1&t||2&t||null===r))e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var o=r.stateNode.containerInfo;if(o===a)break;if(4===i)for(i=r.return;null!==i;){var u=i.tag;if((3===u||4===u)&&i.stateNode.containerInfo===a)return;i=i.return}for(;null!==o;){if(null===(i=qe(o)))return;if(5===(u=i.tag)||6===u||26===u||27===u){r=s=i;continue e}o=o.parentNode}}r=r.return}$t((function(){var r=s,a=Ft(n),i=[];e:{var o=Nr.get(e);if(void 0!==o){var u=rn,c=e;switch(e){case"keypress":if(0===Yt(n))break e;case"keydown":case"keyup":u=yn;break;case"focusin":c="focus",u=cn;break;case"focusout":c="blur",u=cn;break;case"beforeblur":case"afterblur":u=cn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":u=on;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":u=un;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":u=wn;break;case br:case vr:case yr:u=dn;break;case Sr:u=kn;break;case"scroll":case"scrollend":u=ln;break;case"wheel":u=Sn;break;case"copy":case"cut":case"paste":u=fn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":u=xn;break;case"toggle":case"beforetoggle":u=Nn}var d=!!(4&t),f=!d&&("scroll"===e||"scrollend"===e),m=d?null!==o?o+"Capture":null:o;d=[];for(var p,h=r;null!==h;){var g=h;if(p=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===p||null===m||null!=(g=Ut(h,m))&&d.push(Qc(h,g,p)),f)break;h=h.return}0<d.length&&(o=new u(o,c,null,n,a),i.push({event:o,listeners:d}))}}if(!(7&t)){if(u="mouseout"===e||"pointerout"===e,(!(o="mouseover"===e||"pointerover"===e)||n===Mt||!(c=n.relatedTarget||n.fromElement)||!qe(c)&&!c[Ae])&&(u||o)&&(o=a.window===a?a:(o=a.ownerDocument)?o.defaultView||o.parentWindow:window,u?(u=r,null!==(c=(c=n.relatedTarget||n.toElement)?qe(c):null)&&(f=l(c),d=c.tag,c!==f||5!==d&&27!==d&&6!==d)&&(c=null)):(u=null,c=r),u!==c)){if(d=on,g="onMouseLeave",m="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(d=xn,g="onPointerLeave",m="onPointerEnter",h="pointer"),f=null==u?o:Qe(u),p=null==c?o:Qe(c),(o=new d(g,h+"leave",u,n,a)).target=f,o.relatedTarget=p,g=null,qe(a)===r&&((d=new d(m,h+"enter",c,n,a)).target=p,d.relatedTarget=f,g=d),f=g,u&&c)e:{for(m=c,h=0,p=d=u;p;p=Yc(p))h++;for(p=0,g=m;g;g=Yc(g))p++;for(;0<h-p;)d=Yc(d),h--;for(;0<p-h;)m=Yc(m),p--;for(;h--;){if(d===m||null!==m&&d===m.alternate)break e;d=Yc(d),m=Yc(m)}d=null}else d=null;null!==u&&Gc(i,o,u,d,!1),null!==c&&null!==f&&Gc(i,f,c,d,!0)}if("select"===(u=(o=r?Qe(r):window).nodeName&&o.nodeName.toLowerCase())||"input"===u&&"file"===o.type)var b=Vn;else if(Rn(o))if(Bn)b=Xn;else{b=Gn;var v=Yn}else!(u=o.nodeName)||"input"!==u.toLowerCase()||"checkbox"!==o.type&&"radio"!==o.type?r&&zt(r.elementType)&&(b=Vn):b=Kn;switch(b&&(b=b(e,r))?Dn(i,b,n,a):(v&&v(e,o,r),"focusout"===e&&r&&"number"===o.type&&null!=r.memoizedProps.value&&kt(o,"number",o.value)),v=r?Qe(r):window,e){case"focusin":(Rn(v)||"true"===v.contentEditable)&&(ir=v,or=r,ur=null);break;case"focusout":ur=or=ir=null;break;case"mousedown":cr=!0;break;case"contextmenu":case"mouseup":case"dragend":cr=!1,dr(i,n,a);break;case"selectionchange":if(sr)break;case"keydown":case"keyup":dr(i,n,a)}var y;if(Cn)e:{switch(e){case"compositionstart":var x="onCompositionStart";break e;case"compositionend":x="onCompositionEnd";break e;case"compositionupdate":x="onCompositionUpdate";break e}x=void 0}else Mn?Ln(e,n)&&(x="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(x="onCompositionStart");x&&(Pn&&"ko"!==n.locale&&(Mn||"onCompositionStart"!==x?"onCompositionEnd"===x&&Mn&&(y=Zt()):(Wt="value"in(qt=a)?qt.value:qt.textContent,Mn=!0)),0<(v=Zc(r,x)).length&&(x=new mn(x,e,null,n,a),i.push({event:x,listeners:v}),y?x.data=y:null!==(y=On(n))&&(x.data=y))),(y=Tn?function(e,t){switch(e){case"compositionend":return On(t);case"keypress":return 32!==t.which?null:(_n=!0,zn);case"textInput":return(e=t.data)===zn&&_n?null:e;default:return null}}(e,n):function(e,t){if(Mn)return"compositionend"===e||!Cn&&Ln(e,t)?(e=Zt(),Qt=Wt=qt=null,Mn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Pn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(x=Zc(r,"onBeforeInput")).length&&(v=new mn("onBeforeInput","beforeinput",null,n,a),i.push({event:v,listeners:x}),v.data=y)),function(e,t,n,r,a){if("submit"===t&&n&&n.stateNode===a){var l=Mc((a[De]||null).action),s=r.submitter;s&&null!==(t=(t=s[De]||null)?Mc(t.formAction):s.getAttribute("formAction"))&&(l=t,s=null);var i=new rn("action","action",null,r,a);e.push({event:i,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==jc){var e=s?Fc(a,s):new FormData(a);Ms(n,{pending:!0,data:e,method:a.method,action:l},null,e)}}else"function"==typeof l&&(i.preventDefault(),e=s?Fc(a,s):new FormData(a),Ms(n,{pending:!0,data:e,method:a.method,action:l},l,e))},currentTarget:a}]})}}(i,e,r,n,a)}$c(i,t)}))}function Qc(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Zc(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,l=a.stateNode;if(5!==(a=a.tag)&&26!==a&&27!==a||null===l||(null!=(a=Ut(e,n))&&r.unshift(Qc(e,a,l)),null!=(a=Ut(e,t))&&r.push(Qc(e,a,l))),3===e.tag)return r;e=e.return}return[]}function Yc(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function Gc(e,t,n,r,a){for(var l=t._reactName,s=[];null!==n&&n!==r;){var i=n,o=i.alternate,u=i.stateNode;if(i=i.tag,null!==o&&o===r)break;5!==i&&26!==i&&27!==i||null===u||(o=u,a?null!=(u=Ut(n,l))&&s.unshift(Qc(n,u,o)):a||null!=(u=Ut(n,l))&&s.push(Qc(n,u,o))),n=n.return}0!==s.length&&e.push({event:t,listeners:s})}var Kc=/\r\n?/g,Xc=/\u0000|\uFFFD/g;function Jc(e){return("string"==typeof e?e:""+e).replace(Kc,"\n").replace(Xc,"")}function ed(e,t){return t=Jc(t),Jc(e)===t}function td(){}function nd(e,t,n,a,l,s){switch(n){case"children":"string"==typeof a?"body"===t||"textarea"===t&&""===a||Ct(e,a):("number"==typeof a||"bigint"==typeof a)&&"body"!==t&&Ct(e,""+a);break;case"className":st(e,"class",a);break;case"tabIndex":st(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":st(e,n,a);break;case"style":Pt(e,a,s);break;case"data":if("object"!==t){st(e,"data",a);break}case"src":case"href":if(""===a&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==a||"function"==typeof a||"symbol"==typeof a||"boolean"==typeof a){e.removeAttribute(n);break}a=Ot(""+a),e.setAttribute(n,a);break;case"action":case"formAction":if("function"==typeof a){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"==typeof s&&("formAction"===n?("input"!==t&&nd(e,t,"name",l.name,l,null),nd(e,t,"formEncType",l.formEncType,l,null),nd(e,t,"formMethod",l.formMethod,l,null),nd(e,t,"formTarget",l.formTarget,l,null)):(nd(e,t,"encType",l.encType,l,null),nd(e,t,"method",l.method,l,null),nd(e,t,"target",l.target,l,null))),null==a||"symbol"==typeof a||"boolean"==typeof a){e.removeAttribute(n);break}a=Ot(""+a),e.setAttribute(n,a);break;case"onClick":null!=a&&(e.onclick=td);break;case"onScroll":null!=a&&Uc("scroll",e);break;case"onScrollEnd":null!=a&&Uc("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=a){if("object"!=typeof a||!("__html"in a))throw Error(r(61));if(null!=(n=a.__html)){if(null!=l.children)throw Error(r(60));e.innerHTML=n}}break;case"multiple":e.multiple=a&&"function"!=typeof a&&"symbol"!=typeof a;break;case"muted":e.muted=a&&"function"!=typeof a&&"symbol"!=typeof a;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==a||"function"==typeof a||"boolean"==typeof a||"symbol"==typeof a){e.removeAttribute("xlink:href");break}n=Ot(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=a&&"function"!=typeof a&&"symbol"!=typeof a?e.setAttribute(n,""+a):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&"function"!=typeof a&&"symbol"!=typeof a?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===a?e.setAttribute(n,""):!1!==a&&null!=a&&"function"!=typeof a&&"symbol"!=typeof a?e.setAttribute(n,a):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=a&&"function"!=typeof a&&"symbol"!=typeof a&&!isNaN(a)&&1<=a?e.setAttribute(n,a):e.removeAttribute(n);break;case"rowSpan":case"start":null==a||"function"==typeof a||"symbol"==typeof a||isNaN(a)?e.removeAttribute(n):e.setAttribute(n,a);break;case"popover":Uc("beforetoggle",e),Uc("toggle",e),lt(e,"popover",a);break;case"xlinkActuate":it(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":it(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":it(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":it(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":it(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":it(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":it(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":it(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":it(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":lt(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&lt(e,n=_t.get(n)||n,a)}}function rd(e,t,n,a,l,s){switch(n){case"style":Pt(e,a,s);break;case"dangerouslySetInnerHTML":if(null!=a){if("object"!=typeof a||!("__html"in a))throw Error(r(61));if(null!=(n=a.__html)){if(null!=l.children)throw Error(r(60));e.innerHTML=n}}break;case"children":"string"==typeof a?Ct(e,a):("number"==typeof a||"bigint"==typeof a)&&Ct(e,""+a);break;case"onScroll":null!=a&&Uc("scroll",e);break;case"onScrollEnd":null!=a&&Uc("scrollend",e);break;case"onClick":null!=a&&(e.onclick=td);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Ke.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(l=n.endsWith("Capture"),t=n.slice(2,l?n.length-7:void 0),"function"==typeof(s=null!=(s=e[De]||null)?s[n]:null)&&e.removeEventListener(t,s,l),"function"!=typeof a)?n in e?e[n]=a:!0===a?e.setAttribute(n,""):lt(e,n,a):("function"!=typeof s&&null!==s&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,a,l)))}}function ad(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Uc("error",e),Uc("load",e);var a,l=!1,s=!1;for(a in n)if(n.hasOwnProperty(a)){var i=n[a];if(null!=i)switch(a){case"src":l=!0;break;case"srcSet":s=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:nd(e,t,a,i,n,null)}}return s&&nd(e,t,"srcSet",n.srcSet,n,null),void(l&&nd(e,t,"src",n.src,n,null));case"input":Uc("invalid",e);var o=a=i=s=null,u=null,c=null;for(l in n)if(n.hasOwnProperty(l)){var d=n[l];if(null!=d)switch(l){case"name":s=d;break;case"type":i=d;break;case"checked":u=d;break;case"defaultChecked":c=d;break;case"value":a=d;break;case"defaultValue":o=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(r(137,t));break;default:nd(e,t,l,d,n,null)}}return wt(e,a,o,u,c,i,s,!1),void ht(e);case"select":for(s in Uc("invalid",e),l=i=a=null,n)if(n.hasOwnProperty(s)&&null!=(o=n[s]))switch(s){case"value":a=o;break;case"defaultValue":i=o;break;case"multiple":l=o;default:nd(e,t,s,o,n,null)}return t=a,n=i,e.multiple=!!l,void(null!=t?St(e,!!l,t,!1):null!=n&&St(e,!!l,n,!0));case"textarea":for(i in Uc("invalid",e),a=s=l=null,n)if(n.hasOwnProperty(i)&&null!=(o=n[i]))switch(i){case"value":l=o;break;case"defaultValue":s=o;break;case"children":a=o;break;case"dangerouslySetInnerHTML":if(null!=o)throw Error(r(91));break;default:nd(e,t,i,o,n,null)}return jt(e,l,s,a),void ht(e);case"option":for(u in n)if(n.hasOwnProperty(u)&&null!=(l=n[u]))if("selected"===u)e.selected=l&&"function"!=typeof l&&"symbol"!=typeof l;else nd(e,t,u,l,n,null);return;case"dialog":Uc("beforetoggle",e),Uc("toggle",e),Uc("cancel",e),Uc("close",e);break;case"iframe":case"object":Uc("load",e);break;case"video":case"audio":for(l=0;l<Ac.length;l++)Uc(Ac[l],e);break;case"image":Uc("error",e),Uc("load",e);break;case"details":Uc("toggle",e);break;case"embed":case"source":case"link":Uc("error",e),Uc("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(c in n)if(n.hasOwnProperty(c)&&null!=(l=n[c]))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:nd(e,t,c,l,n,null)}return;default:if(zt(t)){for(d in n)n.hasOwnProperty(d)&&(void 0!==(l=n[d])&&rd(e,t,d,l,n,void 0));return}}for(o in n)n.hasOwnProperty(o)&&(null!=(l=n[o])&&nd(e,t,o,l,n,null))}var ld=null,sd=null;function id(e){return 9===e.nodeType?e:e.ownerDocument}function od(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function ud(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function cd(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"bigint"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var dd=null;var fd="function"==typeof setTimeout?setTimeout:void 0,md="function"==typeof clearTimeout?clearTimeout:void 0,pd="function"==typeof Promise?Promise:void 0,hd="function"==typeof queueMicrotask?queueMicrotask:void 0!==pd?function(e){return pd.resolve(null).then(e).catch(gd)}:fd;function gd(e){setTimeout((function(){throw e}))}function bd(e){return"head"===e}function vd(e,t){var n=t,r=0,a=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&8===l.nodeType)if("/$"===(n=l.data)){if(0<r&&8>r){n=r;var s=e.ownerDocument;if(1&n&&jd(s.documentElement),2&n&&jd(s.body),4&n)for(jd(n=s.head),s=n.firstChild;s;){var i=s.nextSibling,o=s.nodeName;s[Be]||"SCRIPT"===o||"STYLE"===o||"LINK"===o&&"stylesheet"===s.rel.toLowerCase()||n.removeChild(s),s=i}}if(0===a)return e.removeChild(l),void Lf(t);a--}else"$"===n||"$?"===n||"$!"===n?a++:r=n.charCodeAt(0)-48;else r=0;n=l}while(n);Lf(t)}function yd(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":yd(n),He(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function xd(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function wd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var kd=null;function Sd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function Nd(e,t,n){switch(t=id(n),e){case"html":if(!(e=t.documentElement))throw Error(r(452));return e;case"head":if(!(e=t.head))throw Error(r(453));return e;case"body":if(!(e=t.body))throw Error(r(454));return e;default:throw Error(r(451))}}function jd(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);He(e)}var Cd=new Map,Ed=new Set;function Td(e){return"function"==typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Pd=I.d;I.d={f:function(){var e=Pd.f(),t=qu();return e||t},r:function(e){var t=We(e);null!==t&&5===t.tag&&"form"===t.type?Rs(t):Pd.r(e)},D:function(e){Pd.D(e),_d("dns-prefetch",e,null)},C:function(e,t){Pd.C(e,t),_d("preconnect",e,t)},L:function(e,t,n){Pd.L(e,t,n);var r=zd;if(r&&e&&t){var a='link[rel="preload"][as="'+yt(t)+'"]';"image"===t&&n&&n.imageSrcSet?(a+='[imagesrcset="'+yt(n.imageSrcSet)+'"]',"string"==typeof n.imageSizes&&(a+='[imagesizes="'+yt(n.imageSizes)+'"]')):a+='[href="'+yt(e)+'"]';var l=a;switch(t){case"style":l=Od(e);break;case"script":l=Rd(e)}Cd.has(l)||(e=u({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),Cd.set(l,e),null!==r.querySelector(a)||"style"===t&&r.querySelector(Md(l))||"script"===t&&r.querySelector(Dd(l))||(ad(t=r.createElement("link"),"link",e),Ye(t),r.head.appendChild(t)))}},m:function(e,t){Pd.m(e,t);var n=zd;if(n&&e){var r=t&&"string"==typeof t.as?t.as:"script",a='link[rel="modulepreload"][as="'+yt(r)+'"][href="'+yt(e)+'"]',l=a;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":l=Rd(e)}if(!Cd.has(l)&&(e=u({rel:"modulepreload",href:e},t),Cd.set(l,e),null===n.querySelector(a))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Dd(l)))return}ad(r=n.createElement("link"),"link",e),Ye(r),n.head.appendChild(r)}}},X:function(e,t){Pd.X(e,t);var n=zd;if(n&&e){var r=Ze(n).hoistableScripts,a=Rd(e),l=r.get(a);l||((l=n.querySelector(Dd(a)))||(e=u({src:e,async:!0},t),(t=Cd.get(a))&&Ud(e,t),Ye(l=n.createElement("script")),ad(l,"link",e),n.head.appendChild(l)),l={type:"script",instance:l,count:1,state:null},r.set(a,l))}},S:function(e,t,n){Pd.S(e,t,n);var r=zd;if(r&&e){var a=Ze(r).hoistableStyles,l=Od(e);t=t||"default";var s=a.get(l);if(!s){var i={loading:0,preload:null};if(s=r.querySelector(Md(l)))i.loading=5;else{e=u({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Cd.get(l))&&$d(e,n);var o=s=r.createElement("link");Ye(o),ad(o,"link",e),o._p=new Promise((function(e,t){o.onload=e,o.onerror=t})),o.addEventListener("load",(function(){i.loading|=1})),o.addEventListener("error",(function(){i.loading|=2})),i.loading|=4,Id(s,t,r)}s={type:"stylesheet",instance:s,count:1,state:i},a.set(l,s)}}},M:function(e,t){Pd.M(e,t);var n=zd;if(n&&e){var r=Ze(n).hoistableScripts,a=Rd(e),l=r.get(a);l||((l=n.querySelector(Dd(a)))||(e=u({src:e,async:!0,type:"module"},t),(t=Cd.get(a))&&Ud(e,t),Ye(l=n.createElement("script")),ad(l,"link",e),n.head.appendChild(l)),l={type:"script",instance:l,count:1,state:null},r.set(a,l))}}};var zd="undefined"==typeof document?null:document;function _d(e,t,n){var r=zd;if(r&&"string"==typeof t&&t){var a=yt(t);a='link[rel="'+e+'"][href="'+a+'"]',"string"==typeof n&&(a+='[crossorigin="'+n+'"]'),Ed.has(a)||(Ed.add(a),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(a)&&(ad(t=r.createElement("link"),"link",e),Ye(t),r.head.appendChild(t)))}}function Ld(e,t,n,a){var l,s,i,o,u=(u=Z.current)?Td(u):null;if(!u)throw Error(r(446));switch(e){case"meta":case"title":return null;case"style":return"string"==typeof n.precedence&&"string"==typeof n.href?(t=Od(n.href),(a=(n=Ze(u).hoistableStyles).get(t))||(a={type:"style",instance:null,count:0,state:null},n.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"==typeof n.href&&"string"==typeof n.precedence){e=Od(n.href);var c=Ze(u).hoistableStyles,d=c.get(e);if(d||(u=u.ownerDocument||u,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,d),(c=u.querySelector(Md(e)))&&!c._p&&(d.instance=c,d.state.loading=5),Cd.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Cd.set(e,n),c||(l=u,s=e,i=n,o=d.state,l.querySelector('link[rel="preload"][as="style"]['+s+"]")?o.loading=1:(s=l.createElement("link"),o.preload=s,s.addEventListener("load",(function(){return o.loading|=1})),s.addEventListener("error",(function(){return o.loading|=2})),ad(s,"link",i),Ye(s),l.head.appendChild(s))))),t&&null===a)throw Error(r(528,""));return d}if(t&&null!==a)throw Error(r(529,""));return null;case"script":return t=n.async,"string"==typeof(n=n.src)&&t&&"function"!=typeof t&&"symbol"!=typeof t?(t=Rd(n),(a=(n=Ze(u).hoistableScripts).get(t))||(a={type:"script",instance:null,count:0,state:null},n.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,e))}}function Od(e){return'href="'+yt(e)+'"'}function Md(e){return'link[rel="stylesheet"]['+e+"]"}function Fd(e){return u({},e,{"data-precedence":e.precedence,precedence:null})}function Rd(e){return'[src="'+yt(e)+'"]'}function Dd(e){return"script[async]"+e}function Ad(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+yt(n.href)+'"]');if(a)return t.instance=a,Ye(a),a;var l=u({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return Ye(a=(e.ownerDocument||e).createElement("style")),ad(a,"style",l),Id(a,n.precedence,e),t.instance=a;case"stylesheet":l=Od(n.href);var s=e.querySelector(Md(l));if(s)return t.state.loading|=4,t.instance=s,Ye(s),s;a=Fd(n),(l=Cd.get(l))&&$d(a,l),Ye(s=(e.ownerDocument||e).createElement("link"));var i=s;return i._p=new Promise((function(e,t){i.onload=e,i.onerror=t})),ad(s,"link",a),t.state.loading|=4,Id(s,n.precedence,e),t.instance=s;case"script":return s=Rd(n.src),(l=e.querySelector(Dd(s)))?(t.instance=l,Ye(l),l):(a=n,(l=Cd.get(s))&&Ud(a=u({},n),l),Ye(l=(e=e.ownerDocument||e).createElement("script")),ad(l,"link",a),e.head.appendChild(l),t.instance=l);case"void":return null;default:throw Error(r(443,t.type))}else"stylesheet"===t.type&&!(4&t.state.loading)&&(a=t.instance,t.state.loading|=4,Id(a,n.precedence,e));return t.instance}function Id(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=r.length?r[r.length-1]:null,l=a,s=0;s<r.length;s++){var i=r[s];if(i.dataset.precedence===t)l=i;else if(l!==a)break}l?l.parentNode.insertBefore(e,l.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function $d(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function Ud(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var Vd=null;function Bd(e,t,n){if(null===Vd){var r=new Map,a=Vd=new Map;a.set(n,r)}else(r=(a=Vd).get(n))||(r=new Map,a.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),a=0;a<n.length;a++){var l=n[a];if(!(l[Be]||l[Re]||"link"===e&&"stylesheet"===l.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==l.namespaceURI){var s=l.getAttribute(t)||"";s=e+s;var i=r.get(s);i?i.push(l):r.set(s,[l])}}return r}function Hd(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function qd(e){return!!("stylesheet"!==e.type||3&e.state.loading)}var Wd=null;function Qd(){}function Zd(){if(this.count--,0===this.count)if(this.stylesheets)Gd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var Yd=null;function Gd(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,Yd=new Map,t.forEach(Kd,e),Yd=null,Zd.call(e))}function Kd(e,t){if(!(4&t.state.loading)){var n=Yd.get(e);if(n)var r=n.get(null);else{n=new Map,Yd.set(e,n);for(var a=e.querySelectorAll("link[data-precedence],style[data-precedence]"),l=0;l<a.length;l++){var s=a[l];"LINK"!==s.nodeName&&"not all"===s.getAttribute("media")||(n.set(s.dataset.precedence,s),r=s)}r&&n.set(null,r)}s=(a=t.instance).getAttribute("data-precedence"),(l=n.get(s)||r)===r&&n.set(null,a),n.set(s,a),this.count++,r=Zd.bind(this),a.addEventListener("load",r),a.addEventListener("error",r),l?l.parentNode.insertBefore(a,l.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(a,e.firstChild),t.state.loading|=4}}var Xd={$$typeof:w,Provider:null,Consumer:null,_currentValue:$,_currentValue2:$,_threadCount:0};function Jd(e,t,n,r,a,l,s,i){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Te(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Te(0),this.hiddenUpdates=Te(null),this.identifierPrefix=r,this.onUncaughtError=a,this.onCaughtError=l,this.onRecoverableError=s,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=i,this.incompleteTransitions=new Map}function ef(e,t,n,r,a,l,s,i,o,u,c,d){return e=new Jd(e,t,n,s,i,o,u,d),t=1,!0===l&&(t|=24),l=$r(3,null,null,t),e.current=l,l.stateNode=e,(t=Da()).refCount++,e.pooledCache=t,t.refCount++,l.memoizedState={element:r,isDehydrated:n,cache:t},sl(l),e}function tf(e){return e?e=Ar:Ar}function nf(e,t,n,r,a,l){a=tf(a),null===r.context?r.context=a:r.pendingContext=a,(r=ol(t)).payload={element:n},null!==(l=void 0===l?null:l)&&(r.callback=l),null!==(n=ul(e,r,t))&&($u(n,0,t),cl(n,e,t))}function rf(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function af(e,t){rf(e,t),(e=e.alternate)&&rf(e,t)}function lf(e){if(13===e.tag){var t=Fr(e,67108864);null!==t&&$u(t,0,67108864),af(e,67108864)}}var sf=!0;function of(e,t,n,r){var a=A.T;A.T=null;var l=I.p;try{I.p=2,cf(e,t,n,r)}finally{I.p=l,A.T=a}}function uf(e,t,n,r){var a=A.T;A.T=null;var l=I.p;try{I.p=8,cf(e,t,n,r)}finally{I.p=l,A.T=a}}function cf(e,t,n,r){if(sf){var a=df(r);if(null===a)Wc(e,t,r,ff,n),Sf(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return gf=Nf(gf,e,t,n,r,a),!0;case"dragenter":return bf=Nf(bf,e,t,n,r,a),!0;case"mouseover":return vf=Nf(vf,e,t,n,r,a),!0;case"pointerover":var l=a.pointerId;return yf.set(l,Nf(yf.get(l)||null,e,t,n,r,a)),!0;case"gotpointercapture":return l=a.pointerId,xf.set(l,Nf(xf.get(l)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(Sf(e,r),4&t&&-1<kf.indexOf(e)){for(;null!==a;){var l=We(a);if(null!==l)switch(l.tag){case 3:if((l=l.stateNode).current.memoizedState.isDehydrated){var s=ke(l.pendingLanes);if(0!==s){var i=l;for(i.pendingLanes|=2,i.entangledLanes|=2;s;){var o=1<<31-be(s);i.entanglements[1]|=o,s&=~o}Cc(l),!(6&su)&&(Cu=le()+500,Ec(0))}}break;case 13:null!==(i=Fr(l,2))&&$u(i,0,2),qu(),af(l,2)}if(null===(l=df(r))&&Wc(e,t,r,ff,n),l===a)break;a=l}null!==a&&r.stopPropagation()}else Wc(e,t,r,null,n)}}function df(e){return mf(e=Ft(e))}var ff=null;function mf(e){if(ff=null,null!==(e=qe(e))){var t=l(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=s(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return ff=e,null}function pf(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(se()){case ie:return 2;case oe:return 8;case ue:case ce:return 32;case de:return 268435456;default:return 32}default:return 32}}var hf=!1,gf=null,bf=null,vf=null,yf=new Map,xf=new Map,wf=[],kf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Sf(e,t){switch(e){case"focusin":case"focusout":gf=null;break;case"dragenter":case"dragleave":bf=null;break;case"mouseover":case"mouseout":vf=null;break;case"pointerover":case"pointerout":yf.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":xf.delete(t.pointerId)}}function Nf(e,t,n,r,a,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[a]},null!==t&&(null!==(t=We(t))&&lf(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function jf(e){var t=qe(e.target);if(null!==t){var n=l(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=s(n)))return e.blockedOn=t,void function(e,t){var n=I.p;try{return I.p=e,t()}finally{I.p=n}}(e.priority,(function(){if(13===n.tag){var e=Au();e=Le(e);var t=Fr(n,e);null!==t&&$u(t,0,e),af(n,e)}}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Cf(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=df(e.nativeEvent);if(null!==n)return null!==(t=We(n))&&lf(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);Mt=r,n.target.dispatchEvent(r),Mt=null,t.shift()}return!0}function Ef(e,t,n){Cf(e)&&n.delete(t)}function Tf(){hf=!1,null!==gf&&Cf(gf)&&(gf=null),null!==bf&&Cf(bf)&&(bf=null),null!==vf&&Cf(vf)&&(vf=null),yf.forEach(Ef),xf.forEach(Ef)}function Pf(t,n){t.blockedOn===n&&(t.blockedOn=null,hf||(hf=!0,e.unstable_scheduleCallback(e.unstable_NormalPriority,Tf)))}var zf=null;function _f(t){zf!==t&&(zf=t,e.unstable_scheduleCallback(e.unstable_NormalPriority,(function(){zf===t&&(zf=null);for(var e=0;e<t.length;e+=3){var n=t[e],r=t[e+1],a=t[e+2];if("function"!=typeof r){if(null===mf(r||n))continue;break}var l=We(n);null!==l&&(t.splice(e,3),e-=3,Ms(l,{pending:!0,data:a,method:n.method,action:r},r,a))}})))}function Lf(e){function t(t){return Pf(t,e)}null!==gf&&Pf(gf,e),null!==bf&&Pf(bf,e),null!==vf&&Pf(vf,e),yf.forEach(t),xf.forEach(t);for(var n=0;n<wf.length;n++){var r=wf[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<wf.length&&null===(n=wf[0]).blockedOn;)jf(n),null===n.blockedOn&&wf.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var a=n[r],l=n[r+1],s=a[De]||null;if("function"==typeof l)s||_f(n);else if(s){var i=null;if(l&&l.hasAttribute("formAction")){if(a=l,s=l[De]||null)i=s.formAction;else if(null!==mf(a))continue}else i=s.action;"function"==typeof i?n[r+1]=i:(n.splice(r,3),r-=3),_f(n)}}}function Of(e){this._internalRoot=e}function Mf(e){this._internalRoot=e}Mf.prototype.render=Of.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(r(409));nf(t.current,Au(),e,t,null,null)},Mf.prototype.unmount=Of.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;nf(e.current,2,null,e,null,null),qu(),t[Ae]=null}},Mf.prototype.unstable_scheduleHydration=function(e){if(e){var t=Me();e={blockedOn:null,target:e,priority:t};for(var n=0;n<wf.length&&0!==t&&t<wf[n].priority;n++);wf.splice(n,0,e),0===n&&jf(e)}};var Ff=t.version;if("19.1.0"!==Ff)throw Error(r(527,Ff,"19.1.0"));I.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(r(188));throw e=Object.keys(e).join(","),Error(r(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=l(e)))throw Error(r(188));return t!==e?null:e}for(var n=e,a=t;;){var s=n.return;if(null===s)break;var o=s.alternate;if(null===o){if(null!==(a=s.return)){n=a;continue}break}if(s.child===o.child){for(o=s.child;o;){if(o===n)return i(s),e;if(o===a)return i(s),t;o=o.sibling}throw Error(r(188))}if(n.return!==a.return)n=s,a=o;else{for(var u=!1,c=s.child;c;){if(c===n){u=!0,n=s,a=o;break}if(c===a){u=!0,a=s,n=o;break}c=c.sibling}if(!u){for(c=o.child;c;){if(c===n){u=!0,n=o,a=s;break}if(c===a){u=!0,a=o,n=s;break}c=c.sibling}if(!u)throw Error(r(189))}}if(n.alternate!==a)throw Error(r(190))}if(3!==n.tag)throw Error(r(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?o(e):null)?null:e.stateNode};var Rf={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:A,reconcilerVersion:"19.1.0"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Df=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Df.isDisabled&&Df.supportsFiber)try{pe=Df.inject(Rf),he=Df}catch(If){}}return b.createRoot=function(e,t){if(!a(e))throw Error(r(299));var n=!1,l="",s=wi,i=ki,o=Si;return null!=t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(l=t.identifierPrefix),void 0!==t.onUncaughtError&&(s=t.onUncaughtError),void 0!==t.onCaughtError&&(i=t.onCaughtError),void 0!==t.onRecoverableError&&(o=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=ef(e,1,!1,null,0,n,l,s,i,o,0,null),e[Ae]=t.current,Hc(e),new Of(t)},b.hydrateRoot=function(e,t,n){if(!a(e))throw Error(r(299));var l=!1,s="",i=wi,o=ki,u=Si,c=null;return null!=n&&(!0===n.unstable_strictMode&&(l=!0),void 0!==n.identifierPrefix&&(s=n.identifierPrefix),void 0!==n.onUncaughtError&&(i=n.onUncaughtError),void 0!==n.onCaughtError&&(o=n.onCaughtError),void 0!==n.onRecoverableError&&(u=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(c=n.formState)),(t=ef(e,1,!0,t,0,l,s,i,o,u,0,c)).context=tf(null),n=t.current,(s=ol(l=Le(l=Au()))).callback=null,ul(n,s,l),n=l,t.current.lanes=n,Pe(t,n),Cc(t),e[Ae]=t.current,Hc(e),new Mf(t)},b.version="19.1.0",b}const P=e((N||(N=1,function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){}}(),g.exports=T()),g.exports)),z="http://192.168.0.152",_="/settings",L="/settings-page",O=z;async function M(e){const t=e.headers.get("content-type");if(!e.ok){let n;try{if(t&&t.includes("application/json")){const t=await e.json();n=t.error||t.message||JSON.stringify(t)}else n=await e.text()}catch{n=`${e.status} ${e.statusText}`}throw new Error(`API Error: ${e.status} ${e.statusText} - ${n}`)}return t&&t.includes("application/json")?e.json():e.text()}async function F(){return M(await fetch(`${O}/status`))}async function R(e){return M(await fetch(`${O}/settings`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}))}async function D(e){return M(await fetch(`${O}/brightness`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}))}async function A(e=128){return async function(e=128){return M(await fetch(`${O}/calibrate/white`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({brightness:e})}))}(e)}const I=({title:e,children:t,className:n,titleClassName:r})=>i.jsxs("div",{className:`bg-slate-800 shadow-md border border-slate-700 rounded-xl p-6 ${n||""}`,children:[e&&i.jsx("h2",{className:`text-xl font-semibold text-slate-200 mb-4 ${r||""}`,children:e}),t]}),$=({r:e,g:t,b:n,size:r="md",className:a,showRgbText:l=!1})=>{const s=`rgb(${e}, ${t}, ${n})`,o=.299*e+.587*t+.114*n>186?"#000000":"#FFFFFF";return i.jsx("div",{className:`rounded-md shadow-sm border border-slate-600 flex items-center justify-center ${{sm:"w-8 h-8",md:"w-12 h-12",lg:"w-20 h-20",xl:"w-32 h-32"}[r]} ${a||""}`,style:{backgroundColor:s},children:l&&i.jsxs("span",{style:{color:o},className:"text-xs font-mono",children:[e,",",t,",",n]})})},U=({size:e="md",className:t,message:n})=>i.jsxs("div",{className:`flex flex-col items-center justify-center ${t||""}`,children:[i.jsx("div",{className:`spinner ${{sm:"w-6 h-6 border-2",md:"w-8 h-8 border-4",lg:"w-12 h-12 border-4"}[e]} border-slate-600 border-t-blue-500 rounded-full animate-spin`}),n&&i.jsx("p",{className:"mt-2 text-sm text-slate-400",children:n})]}),V=({label:e,value:t})=>i.jsxs("div",{className:"flex justify-between py-1 border-b border-slate-700 last:border-b-0",children:[i.jsxs("span",{className:"text-sm text-slate-400",children:[e,":"]}),i.jsx("span",{className:"text-sm font-medium text-slate-100 text-right",children:t})]}),B=({status:e,isLoading:t,error:n,onRefresh:r})=>t&&!e?i.jsx(I,{title:"Device Status",children:i.jsx(U,{message:"Loading status..."})}):n?i.jsx(I,{title:"Device Status",children:i.jsxs("p",{className:"text-red-400",children:["Error: ",n]})}):e?i.jsxs(I,{title:"Device Status",className:"mb-6",children:[i.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-2",children:[i.jsx(V,{label:"Scanning",value:e.isScanning?"Yes":"No"}),i.jsx(V,{label:"LED On",value:e.ledState?"Yes":"No"}),i.jsx(V,{label:"Calibrated",value:e.isCalibrated?"Yes":"No"}),i.jsx(V,{label:"Sample Count",value:e.sampleCount}),i.jsx(V,{label:"ATIME",value:e.atime}),i.jsx(V,{label:"AGAIN",value:e.again}),i.jsx(V,{label:"Scan Brightness",value:e.brightness}),i.jsx(V,{label:"Ambient Lux",value:`${e.ambientLux.toFixed(2)} lx`}),e.rssi&&i.jsx(V,{label:"WiFi RSSI",value:`${e.rssi} dBm`}),e.esp32IP&&i.jsx(V,{label:"Device IP",value:e.esp32IP}),i.jsxs("div",{className:"md:col-span-2 flex items-center space-x-2 py-1",children:[i.jsx("span",{className:"text-sm text-slate-400",children:"Current LED Color:"}),i.jsx($,{r:e.currentR,g:e.currentG,b:e.currentB,size:"sm"})]})]}),i.jsx("div",{className:"mt-4 flex justify-end",children:i.jsx("button",{onClick:r,disabled:t,className:"text-sm text-blue-400 hover:text-blue-300 disabled:opacity-50",children:t?"Refreshing...":"Refresh Status"})})]}):i.jsx(I,{title:"Device Status",children:i.jsx("p",{className:"text-slate-400",children:"No status data available."})}),H=({children:e,className:t,variant:n="primary",size:r="md",isLoading:a=!1,disabled:l,...s})=>i.jsxs("button",{type:"button",className:`font-semibold rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-150 ease-in-out flex items-center justify-center ${{primary:"bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500",secondary:"bg-slate-600 hover:bg-slate-700 text-slate-100 focus:ring-slate-500 border border-slate-500",danger:"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500",success:"bg-green-600 hover:bg-green-700 text-white focus:ring-green-500",outline:"bg-transparent hover:bg-slate-100 text-slate-700 focus:ring-slate-500 border border-slate-300"}[n]} ${{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"}[r]} disabled:opacity-50 disabled:cursor-not-allowed ${t||""}`,disabled:l||a,...s,children:[a&&i.jsxs("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[i.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),i.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),e]}),q=({onSampleSaved:e,showToast:t})=>{const[n,r]=f.useState(null),[a,l]=f.useState(!1),[s,o]=f.useState(!1);return i.jsx(I,{title:"Color Scanner",className:"mb-6",children:i.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[i.jsx(H,{onClick:async()=>{l(!0),r(null);try{const e=await async function(){return M(await fetch(`${O}/scan`,{method:"POST"}))}();r(e),t("Scan successful!","success")}catch(e){t(e instanceof Error?e.message:"Scan failed.","error")}finally{l(!1)}},isLoading:a,disabled:a||s,variant:"primary",size:"lg",children:"Scan Color"}),a&&i.jsx(U,{message:"Scanning..."}),n&&i.jsxs("div",{className:"mt-6 p-4 border border-slate-700 rounded-lg shadow-sm w-full max-w-md text-center",children:[i.jsx("h3",{className:"text-lg font-medium text-slate-200 mb-3",children:"Scanned Color:"}),i.jsx("div",{className:"flex justify-center mb-3",children:i.jsx($,{r:n.r,g:n.g,b:n.b,size:"xl"})}),i.jsxs("p",{className:"font-mono text-slate-300",children:["RGB: (",n.r,", ",n.g,", ",n.b,")"]}),i.jsxs("p",{className:"font-mono text-xs text-slate-400",children:["XYZ: (",n.x,", ",n.y,", ",n.z,") IR: ",n.ir]}),i.jsx(H,{onClick:async()=>{if(n){o(!0);try{await async function(e,t,n){return M(await fetch(`${O}/save`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({r:e,g:t,b:n})}))}(n.r,n.g,n.b),t("Sample saved!","success"),e(),r(null)}catch(a){t(a instanceof Error?a.message:"Save sample failed.","error")}finally{o(!1)}}else t("No color scanned to save.","error")},isLoading:s,disabled:a||s,variant:"success",className:"mt-4",children:"Save Sample"})]})]})})},W=["Dark/Low-light","Indoor","Bright","Very Bright"],Q=["1x","4x","16x","64x"],Z=()=>{const[e,t]=f.useState(!1),[n,r]=f.useState(null),[a,l]=f.useState(null),[s,o]=f.useState(null),[u,c]=f.useState(!1),d=e=>new Date(e).toLocaleTimeString();return i.jsx("div",{className:"space-y-6",children:i.jsxs(I,{className:"p-6",children:[i.jsx("h2",{className:"text-xl font-semibold text-slate-100 mb-4",children:"Enhanced Color Scanning"}),i.jsx("p",{className:"text-slate-300 text-sm mb-4",children:"Uses dynamic sensor optimization for accurate color matching across all lighting conditions."}),i.jsxs("div",{className:"flex gap-3 mb-4",children:[i.jsx(H,{onClick:async()=>{t(!0),o(null);try{const e=await fetch(`${z}/enhanced-scan`,{method:"POST",headers:{"Content-Type":"application/json"}});if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const t=await e.json();r(t)}catch(e){o(e instanceof Error?e.message:"Enhanced scan failed")}finally{t(!1)}},disabled:e,className:"flex-1",children:e?i.jsxs(i.Fragment,{children:[i.jsx(U,{size:"sm"}),i.jsx("span",{className:"ml-2",children:"Scanning..."})]}):"Enhanced Scan"}),i.jsx(H,{onClick:async()=>{try{const e=await fetch(`${z}/sensor-diagnostics`);if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const t=await e.json();l(t)}catch(e){o(e instanceof Error?e.message:"Failed to fetch diagnostics")}},variant:"secondary",className:"flex-1",children:"Sensor Diagnostics"})]}),s&&i.jsx("div",{className:"bg-red-900/50 border border-red-500 rounded-lg p-3 mb-4",children:i.jsx("p",{className:"text-red-200 text-sm",children:s})}),n&&i.jsxs("div",{className:"bg-slate-800 rounded-lg p-4 mb-4",children:[i.jsx("h3",{className:"text-lg font-medium text-slate-100 mb-3",children:"Scan Results"}),i.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[i.jsxs("div",{children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Color Values"}),i.jsxs("div",{className:"space-y-1 text-sm",children:[i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"RGB:"}),i.jsxs("span",{className:"text-slate-200",children:["(",n.r,", ",n.g,", ",n.b,")"]})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"XYZ:"}),i.jsxs("span",{className:"text-slate-200",children:["(",n.x,", ",n.y,", ",n.z,")"]})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"IR:"}),i.jsxs("span",{className:"text-slate-200",children:["(",n.ir1,", ",n.ir2,")"]})]})]})]}),i.jsxs("div",{children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Color Preview"}),i.jsx("div",{className:"w-full h-16 rounded border border-slate-600",style:{backgroundColor:`rgb(${n.r}, ${n.g}, ${n.b})`}})]})]}),n.sensorConfig&&i.jsxs("div",{className:"border-t border-slate-600 pt-3",children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Sensor Configuration"}),i.jsxs("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Integration Time:"}),i.jsxs("span",{className:"text-slate-200",children:[n.sensorConfig.atime,"ms"]})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Gain:"}),i.jsx("span",{className:"text-slate-200",children:Q[n.sensorConfig.again]})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Brightness:"}),i.jsx("span",{className:"text-slate-200",children:n.sensorConfig.brightness})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Condition:"}),i.jsx("span",{className:"text-slate-200",children:W[n.sensorConfig.condition]})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Optimal:"}),i.jsx("span",{className:n.sensorConfig.isOptimal?"text-green-400":"text-yellow-400",children:n.sensorConfig.isOptimal?"Yes":"No"})]})]})]}),n.brightnessOptimization&&i.jsxs("div",{className:"border-t border-slate-600 pt-3 mt-3",children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Automatic Brightness Optimization"}),i.jsxs("div",{className:"grid grid-cols-2 gap-2 text-sm",children:[i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Control Variable:"}),i.jsx("span",{className:"text-slate-200",children:n.brightnessOptimization.controlVariable})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Target Range:"}),i.jsxs("span",{className:"text-slate-200",children:[n.brightnessOptimization.targetMin," - ",n.brightnessOptimization.targetMax]})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"In Optimal Range:"}),i.jsx("span",{className:n.brightnessOptimization.inOptimalRange?"text-green-400":"text-yellow-400",children:n.brightnessOptimization.inOptimalRange?"Yes":"No"})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Optimized Brightness:"}),i.jsx("span",{className:"text-slate-200",children:n.brightnessOptimization.optimizedBrightness})]})]})]}),i.jsxs("div",{className:"text-xs text-slate-500 mt-3",children:["Scanned at ",d(n.timestamp)]})]}),a&&i.jsxs("div",{className:"bg-slate-800 rounded-lg p-4",children:[i.jsxs("div",{className:"flex justify-between items-center mb-3",children:[i.jsx("h3",{className:"text-lg font-medium text-slate-100",children:"Sensor Diagnostics"}),i.jsx(H,{onClick:()=>c(!u),variant:"secondary",size:"sm",children:u?"Hide Details":"Show Details"})]}),i.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-3",children:[i.jsxs("div",{children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Current Readings"}),i.jsxs("div",{className:"space-y-1 text-sm",children:[i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"XYZ:"}),i.jsxs("span",{className:"text-slate-200",children:["(",a.currentReadings.x,", ",a.currentReadings.y,", ",a.currentReadings.z,")"]})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Saturated:"}),i.jsx("span",{className:a.currentReadings.saturated?"text-red-400":"text-green-400",children:a.currentReadings.saturated?"Yes":"No"})]})]})]}),i.jsxs("div",{children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Dynamic Sensor"}),i.jsxs("div",{className:"space-y-1 text-sm",children:[i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Enabled:"}),i.jsx("span",{className:a.dynamicSensor.enabled?"text-green-400":"text-red-400",children:a.dynamicSensor.enabled?"Yes":"No"})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Initialized:"}),i.jsx("span",{className:a.dynamicSensor.initialized?"text-green-400":"text-red-400",children:a.dynamicSensor.initialized?"Yes":"No"})]}),void 0!==a.dynamicSensor.detectedCondition&&i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Condition:"}),i.jsx("span",{className:"text-slate-200",children:W[a.dynamicSensor.detectedCondition]})]})]})]})]}),u&&i.jsx("div",{className:"border-t border-slate-600 pt-3",children:i.jsx("pre",{className:"text-xs text-slate-300 bg-slate-900 p-3 rounded overflow-auto max-h-64",children:JSON.stringify(a,null,2)})}),i.jsxs("div",{className:"text-xs text-slate-500 mt-3",children:["Updated at ",d(a.timestamp)]})]})]})})},Y=({sample:e,index:t,onDelete:n})=>{const[r,a]=f.useState(!1);return i.jsx("li",{className:"bg-slate-700 p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-150 relative group",children:i.jsxs("div",{className:"flex items-start space-x-4",children:[i.jsx($,{r:e.r,g:e.g,b:e.b,size:"lg"}),i.jsxs("div",{className:"flex-1",children:[i.jsx("h3",{className:"text-sm font-semibold text-slate-100",children:e.paintName&&"Unknown"!==e.paintName?e.paintName:`RGB: ${e.r}, ${e.g}, ${e.b}`}),e.paintCode&&"N/A"!==e.paintCode&&i.jsxs("p",{className:"text-xs text-slate-300",children:["Code: ",e.paintCode]}),e.paintName&&"Unknown"!==e.paintName&&i.jsxs("p",{className:"text-xs text-slate-400 font-mono",children:["RGB: ",e.r,", ",e.g,", ",e.b]}),e.lrv>0&&i.jsxs("p",{className:"text-xs text-slate-400",children:["LRV: ",e.lrv.toFixed(1)]}),i.jsxs("p",{className:"text-xs text-slate-500 mt-1",children:["Saved: ",new Date(e.timestamp).toLocaleString()]})]}),i.jsx("button",{onClick:async e=>{if(e.preventDefault(),e.stopPropagation(),window.confirm("Are you sure you want to delete this sample?")){a(!0);try{await n(t)}finally{a(!1)}}},disabled:r,className:"absolute top-2 right-2 w-6 h-6 bg-red-600 hover:bg-red-700 disabled:bg-red-800 disabled:opacity-50 text-white rounded-full flex items-center justify-center text-xs font-bold transition-colors duration-150",title:"Delete sample",children:r?i.jsx("div",{className:"w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"}):"×"})]})})},G=({samples:e,isLoading:t,error:n,onSampleDeleted:r,showToast:a})=>{const[l,s]=f.useState(!1),[o,u]=f.useState(!1),c=async e=>{try{const t=await async function(e){return M(await fetch(`${O}/delete`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({index:e})}))}(e);t.success?(a("Sample deleted successfully!","success"),r()):a(t.message||"Failed to delete sample","error")}catch(t){a(t instanceof Error?t.message:"Failed to delete sample","error")}};return t?i.jsx(I,{title:"Saved Samples",children:i.jsx(U,{message:"Loading samples..."})}):n?i.jsx(I,{title:"Saved Samples",children:i.jsxs("p",{className:"text-red-400",children:["Error: ",n]})}):i.jsx(I,{title:"Saved Samples",className:"mb-6",children:0===e.length?i.jsx("p",{className:"text-slate-400",children:"No samples saved yet."}):i.jsxs(i.Fragment,{children:[i.jsx("div",{className:"mb-4 flex justify-end",children:i.jsx("button",{onClick:()=>u(!0),disabled:l,className:"px-3 py-1 bg-red-600 hover:bg-red-700 disabled:bg-red-800 disabled:opacity-50 text-white text-sm rounded transition-colors duration-150",title:"Delete all samples",children:l?i.jsxs("div",{className:"flex items-center",children:[i.jsx("div",{className:"w-3 h-3 border border-white border-t-transparent rounded-full animate-spin mr-2"}),"Deleting..."]}):"Delete All"})}),i.jsx("ul",{className:"space-y-3 max-h-96 overflow-y-auto pr-2",children:e.map(((e,t)=>i.jsx(Y,{sample:e,index:t,onDelete:c},`${e.timestamp}-${t}`)))}),o&&i.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:i.jsxs("div",{className:"bg-slate-800 p-6 rounded-lg shadow-xl max-w-md w-full mx-4",children:[i.jsx("h3",{className:"text-lg font-semibold text-slate-100 mb-4",children:"Confirm Delete All"}),i.jsxs("p",{className:"text-slate-300 mb-6",children:["Are you sure you want to delete all ",e.length," saved samples? This action cannot be undone."]}),i.jsxs("div",{className:"flex justify-end space-x-3",children:[i.jsx("button",{onClick:()=>u(!1),disabled:l,className:"px-4 py-2 bg-slate-600 hover:bg-slate-700 disabled:opacity-50 text-white rounded transition-colors duration-150",children:"Cancel"}),i.jsx("button",{onClick:async()=>{s(!0);try{const e=await async function(){return M(await fetch(`${O}/samples/clear`,{method:"POST",headers:{"Content-Type":"application/json"}}))}();e.success?(a("All samples deleted successfully!","success"),r()):a(e.message||"Failed to delete all samples","error")}catch(e){a(e instanceof Error?e.message:"Failed to delete all samples","error")}finally{s(!1),u(!1)}},disabled:l,className:"px-4 py-2 bg-red-600 hover:bg-red-700 disabled:bg-red-800 disabled:opacity-50 text-white rounded transition-colors duration-150",children:l?"Deleting...":"Delete All"})]})]})})]})})},K=({label:e,id:t,wrapperClassName:n,className:r,...a})=>i.jsxs("div",{className:n,children:[e&&i.jsx("label",{htmlFor:t,className:"block text-sm font-medium text-slate-300 mb-1",children:e}),i.jsx("input",{id:t,className:`block w-full px-3 py-2 border border-slate-600 rounded-md bg-slate-700 text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${r||""}`,...a})]}),X=({id:e,label:t,min:n,max:r,step:a=1,value:l,onChange:s,disabled:o,unit:u})=>i.jsxs("div",{children:[i.jsxs("label",{htmlFor:e,className:"block text-sm font-medium text-slate-300",children:[t,": ",i.jsxs("span",{className:"font-semibold text-slate-100",children:[l,u]})]}),i.jsx("input",{type:"range",id:e,min:n,max:r,step:a,value:l,onChange:e=>s(parseInt(e.target.value,10)),disabled:o,className:"w-full h-2 bg-slate-600 rounded-lg appearance-none cursor-pointer accent-blue-500 disabled:opacity-50 disabled:cursor-not-allowed mt-1"})]}),J=({id:e,label:t,checked:n,onChange:r,disabled:a})=>i.jsxs("div",{className:"flex items-center",children:[i.jsx("button",{type:"button",id:e,role:"switch","aria-checked":n,onClick:()=>!a&&r(!n),className:`${n?"bg-blue-600":"bg-slate-600"} relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-900 ${a?"opacity-50 cursor-not-allowed":""}`,disabled:a,children:i.jsx("span",{"aria-hidden":"true",className:(n?"translate-x-5":"translate-x-0")+" pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out"})}),i.jsx("label",{htmlFor:e,className:"ml-3 text-sm font-medium text-slate-300 cursor-pointer",children:t})]});function ee({isActive:e=!0,updateInterval:t=1500}){const[n,r]=f.useState(null),[a,l]=f.useState(!1),[s,o]=f.useState(null),u=f.useRef(null);f.useEffect((()=>(e?c():d(),()=>d())),[e,t]);const c=()=>{d(),m(),u.current=setInterval(m,t)},d=()=>{u.current&&(clearInterval(u.current),u.current=null)},m=async()=>{try{const e=await fetch(`${z}/live-metrics`,{method:"GET",headers:{Accept:"application/json"}});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);{const t=e.headers.get("content-type");if(!t||!t.includes("application/json"))throw new Error("Live metrics endpoint not available - firmware update needed");{const t=await e.json();r(t),l(!0),o(null)}}}catch(e){l(!1),o(e instanceof Error?e.message:"Unknown error")}},p=e=>{switch(e){case"optimal":case"normal":case"clean":case"adequate":return"text-green-400 bg-green-900/30";case"high":case"low":case"saturated":case"contaminated":return"text-red-400 bg-red-900/30";default:return"text-yellow-400 bg-yellow-900/30"}};return e?i.jsxs("div",{className:"bg-slate-800 rounded-lg p-6 border border-slate-600",children:[i.jsxs("div",{className:"flex items-center justify-between mb-4",children:[i.jsx("h3",{className:"text-lg font-semibold text-slate-200",children:"Live Sensor Metrics"}),i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx("div",{className:"w-2 h-2 rounded-full "+(a?"bg-green-400":"bg-red-400")}),i.jsx("span",{className:"text-xs text-slate-400",children:a?"Connected":"Disconnected"})]})]}),s&&i.jsxs("div",{className:"mb-4 p-3 rounded-md text-sm bg-red-900/30 text-red-400 border border-red-700",children:["Connection Error: ",s]}),n?i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Current Sensor Readings"}),i.jsxs("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[i.jsxs("div",{className:"bg-slate-700/50 rounded p-2",children:[i.jsx("span",{className:"text-slate-400",children:"X (Red):"}),i.jsx("span",{className:"text-slate-200 ml-2 font-mono",children:n.sensorReadings.x})]}),i.jsxs("div",{className:"bg-slate-700/50 rounded p-2",children:[i.jsx("span",{className:"text-slate-400",children:"Y (Green):"}),i.jsx("span",{className:"text-slate-200 ml-2 font-mono",children:n.sensorReadings.y})]}),i.jsxs("div",{className:"bg-slate-700/50 rounded p-2",children:[i.jsx("span",{className:"text-slate-400",children:"Z (Blue):"}),i.jsx("span",{className:"text-slate-200 ml-2 font-mono",children:n.sensorReadings.z})]}),i.jsxs("div",{className:"bg-slate-700/50 rounded p-2",children:[i.jsx("span",{className:"text-slate-400",children:"IR:"}),i.jsx("span",{className:"text-slate-200 ml-2 font-mono",children:n.sensorReadings.ir})]})]})]}),i.jsxs("div",{children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Control Metrics"}),i.jsxs("div",{className:"space-y-2",children:[i.jsxs("div",{className:"flex justify-between items-center",children:[i.jsx("span",{className:"text-slate-400",children:"Control Variable:"}),i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx("span",{className:"text-slate-200 font-mono",children:n.metrics.controlVariable}),i.jsx("span",{className:`px-2 py-1 rounded text-xs ${p(n.statusIndicators.controlVariableStatus)}`,children:n.statusIndicators.controlVariableStatus})]})]}),i.jsxs("div",{className:"flex justify-between items-center",children:[i.jsx("span",{className:"text-slate-400",children:"Target Range:"}),i.jsxs("span",{className:"text-slate-200 font-mono",children:[n.metrics.targetMin," - ",n.metrics.targetMax]})]}),i.jsxs("div",{className:"flex justify-between items-center",children:[i.jsx("span",{className:"text-slate-400",children:"IR Ratio:"}),i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx("span",{className:"text-slate-200 font-mono",children:((e,t=0)=>e.toFixed(t))(n.metrics.irRatio,3)}),i.jsx("span",{className:`px-2 py-1 rounded text-xs ${p(n.statusIndicators.irContaminationStatus)}`,children:n.statusIndicators.irContaminationStatus})]})]})]})]}),i.jsxs("div",{children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"LED Status"}),i.jsxs("div",{className:"space-y-2",children:[i.jsxs("div",{className:"flex justify-between items-center",children:[i.jsx("span",{className:"text-slate-400",children:"Current Brightness:"}),i.jsx("span",{className:"text-slate-200 font-mono",children:n.ledStatus.currentBrightness})]}),i.jsxs("div",{className:"flex justify-between items-center",children:[i.jsx("span",{className:"text-slate-400",children:"Enhanced Mode:"}),i.jsx("span",{className:"px-2 py-1 rounded text-xs "+(n.ledStatus.enhancedMode?"text-green-400 bg-green-900/30":"text-yellow-400 bg-yellow-900/30"),children:n.ledStatus.enhancedMode?"Enabled":"Manual"})]}),!n.ledStatus.enhancedMode&&i.jsxs("div",{className:"flex justify-between items-center",children:[i.jsx("span",{className:"text-slate-400",children:"Manual Intensity:"}),i.jsx("span",{className:"text-slate-200 font-mono",children:n.ledStatus.manualIntensity})]}),i.jsxs("div",{className:"flex justify-between items-center",children:[i.jsx("span",{className:"text-slate-400",children:"Scanning:"}),i.jsx("span",{className:"px-2 py-1 rounded text-xs "+(n.ledStatus.isScanning?"text-blue-400 bg-blue-900/30":"text-slate-400 bg-slate-700/50"),children:n.ledStatus.isScanning?"Active":"Idle"})]})]})]}),i.jsxs("div",{children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300 mb-2",children:"Status Summary"}),i.jsxs("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[i.jsxs("div",{className:`px-2 py-1 rounded ${p(n.statusIndicators.saturationStatus)}`,children:["Saturation: ",n.statusIndicators.saturationStatus]}),i.jsxs("div",{className:`px-2 py-1 rounded ${p(n.statusIndicators.signalStatus)}`,children:["Signal: ",n.statusIndicators.signalStatus]})]})]}),i.jsxs("div",{className:"text-xs text-slate-500 text-center",children:["Last updated: ",new Date(n.timestamp).toLocaleTimeString()]})]}):i.jsxs("div",{className:"text-center text-slate-400",children:[i.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-2"}),"Loading sensor metrics..."]})]}):i.jsxs("div",{className:"bg-slate-800 rounded-lg p-6 border border-slate-600",children:[i.jsx("h3",{className:"text-lg font-semibold text-slate-200 mb-4",children:"Live Sensor Metrics"}),i.jsx("p",{className:"text-slate-400 text-center",children:"Monitoring disabled"})]})}const te=({initialSettings:e,onSettingsChange:t,showToast:n})=>{var r;const[a,l]=f.useState((null==e?void 0:e.atime)||255),[s,o]=f.useState((null==e?void 0:e.again)||0),[u,c]=f.useState(Math.max(64,(null==e?void 0:e.brightness)||128)),[d,m]=f.useState((null==e?void 0:e.ledState)||!1),[p,h]=f.useState(!1),[g,b]=f.useState(!1),[v,y]=f.useState(void 0!==(null==e?void 0:e.autoZeroMode)?e.autoZeroMode:1),[x,w]=f.useState(void 0!==(null==e?void 0:e.autoZeroFreq)?e.autoZeroFreq:127),[k,S]=f.useState(void 0!==(null==e?void 0:e.waitTime)?e.waitTime:0),[N,j]=f.useState(!1),[C,E]=f.useState(!1),[T,P]=f.useState([]),[z,_]=f.useState(!1);f.useEffect((()=>{e&&(l(e.atime),o(e.again),c(e.brightness),m(e.ledState),y(void 0!==e.autoZeroMode?e.autoZeroMode:1),w(void 0!==e.autoZeroFreq?e.autoZeroFreq:127),S(void 0!==e.waitTime?e.waitTime:0))}),[e]);const L=f.useCallback(((e,t,n="")=>{const r={timestamp:(new Date).toLocaleTimeString(),test:e,success:t,message:n};P((e=>[...e,r]))}),[]),D=f.useCallback((()=>{P([])}),[]),$=e=>new Promise((t=>setTimeout(t,e))),U=e=>{const t=[];return void 0!==e.atime&&(e.atime<0||e.atime>255)&&t.push("ATIME must be 0-255"),void 0!==e.again&&(e.again<0||e.again>3)&&t.push("AGAIN must be 0-3"),void 0!==e.brightness&&(e.brightness<64||e.brightness>255)&&t.push("Brightness must be 64-255 (ESP32 hardware limit)"),void 0!==e.autoZeroFreq&&(e.autoZeroFreq<0||e.autoZeroFreq>255)&&t.push("Auto-Zero Frequency must be 0-255"),void 0!==e.waitTime&&(e.waitTime<0||e.waitTime>255)&&t.push("Wait Time must be 0-255"),void 0!==e.autoZeroMode&&(e.autoZeroMode<0||e.autoZeroMode>1)&&t.push("Auto-Zero Mode must be 0-1"),t},V=async e=>{try{L(e.name,!0,`Testing: ${JSON.stringify(e.settings)}`),await R(e.settings),await $(500);const t=await F();if(e.expectedValues){let n=!0;for(const[r,a]of Object.entries(e.expectedValues)){const l=t[r];l!==a&&(L(e.name,!1,`${r}: expected ${a}, got ${l}`),n=!1)}return!!n&&(L(e.name,!0,"All values applied correctly"),!0)}return e.shouldFail?(L(e.name,!0,"Invalid values correctly rejected"),!0):(L(e.name,!0,"Settings updated successfully"),!0)}catch(t){const n=t instanceof Error?t.message:"Unknown error";return e.shouldFail?(L(e.name,!0,`Correctly rejected: ${n}`),!0):(L(e.name,!1,`Failed: ${n}`),!1)}},B=async()=>{L("Valid Values Test",!0,"Starting valid values test...");const e=[{name:"Minimum Values",settings:{autoZeroMode:0,autoZeroFreq:0,waitTime:0},expectedValues:{autoZeroMode:0,autoZeroFreq:0,waitTime:0}},{name:"Recommended Values",settings:{autoZeroMode:1,autoZeroFreq:127,waitTime:5},expectedValues:{autoZeroMode:1,autoZeroFreq:127,waitTime:5}},{name:"Maximum Values",settings:{autoZeroMode:1,autoZeroFreq:255,waitTime:255},expectedValues:{autoZeroMode:1,autoZeroFreq:255,waitTime:255}}];let t=!0;for(const n of e){await V(n)||(t=!1),await $(300)}return L("Valid Values Test",t,t?"All valid values accepted":"Some valid values failed"),t},q=async()=>{L("Invalid Values Test",!0,"Starting invalid values test...");const e=await F(),t=[{name:"Invalid Auto-Zero Mode",settings:{autoZeroMode:5},shouldFail:!0},{name:"Invalid Auto-Zero Frequency",settings:{autoZeroFreq:300},shouldFail:!0},{name:"Invalid Wait Time",settings:{waitTime:300},shouldFail:!0}];let n=!0;for(const a of t){try{await R(a.settings),await $(500);const t=await F();e.autoZeroMode===t.autoZeroMode&&e.autoZeroFreq===t.autoZeroFreq&&e.waitTime===t.waitTime?L(a.name,!0,"Invalid values correctly rejected"):(L(a.name,!1,"Invalid values incorrectly accepted"),n=!1)}catch(r){L(a.name,!0,"Invalid values properly rejected by API")}await $(300)}return L("Invalid Values Test",n,n?"All invalid values rejected":"Some invalid values accepted"),n},W=async()=>{L("Combined Settings Test",!0,"Testing combined settings update...");const e={autoZeroMode:1,autoZeroFreq:127,waitTime:10,atime:56,again:3,brightness:128};try{await R(e),await $(1e3);const t=await F();let n=!0;for(const[r,a]of Object.entries(e)){const e=t[r];e!==a&&(L("Combined Settings Test",!1,`${r}: expected ${a}, got ${e}`),n=!1)}return!!n&&(L("Combined Settings Test",!0,"All combined settings applied correctly"),!0)}catch(t){const e=t instanceof Error?t.message:"Unknown error";return L("Combined Settings Test",!1,`Failed: ${e}`),!1}},Q=[{value:0,label:"1x"},{value:1,label:"4x"},{value:2,label:"16x"},{value:3,label:"64x"}];return i.jsx(I,{title:"Scanner Settings",className:"mb-6",children:i.jsxs("div",{className:"space-y-6",children:[i.jsx(K,{id:"atime",label:"ATIME (Integration Time)",type:"number",min:"0",max:"255",value:a,onChange:e=>l(parseInt(e.target.value,10)),wrapperClassName:"max-w-xs"}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"again",className:"block text-sm font-medium text-slate-300 mb-1",children:"AGAIN (Analog Gain)"}),i.jsx("select",{id:"again",value:s,onChange:e=>o(parseInt(e.target.value,10)),className:"block w-full max-w-xs px-3 py-2 border border-slate-600 rounded-md bg-slate-700 text-slate-100 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",children:Q.map((e=>i.jsx("option",{value:e.value,children:e.label},e.value)))})]}),i.jsx(X,{id:"scan-brightness",label:"Scan Brightness",min:64,max:255,value:u,onChange:c}),i.jsx(J,{id:"ledState",label:"LED Always On (Rainbow if idle)",checked:d,onChange:m}),i.jsx("div",{className:"border-t border-slate-600 pt-6",children:i.jsxs("div",{className:"space-y-4",children:[i.jsx("label",{className:"block text-sm font-medium text-slate-300 mb-3",children:"TCS3430 Advanced Calibration:"}),i.jsxs("div",{className:"bg-green-900/30 p-3 rounded-lg border border-green-600 mb-4",children:[i.jsx("p",{className:"text-sm font-medium text-green-300 mb-2",children:"📋 Recommended Optimal Settings:"}),i.jsxs("div",{className:"text-xs text-green-200 space-y-1",children:[i.jsxs("div",{children:[i.jsx("strong",{children:"ATIME:"})," 150 (Integration Time)"]}),i.jsxs("div",{children:[i.jsx("strong",{children:"AGAIN:"})," 16x (Analog Gain)"]}),i.jsxs("div",{children:[i.jsx("strong",{children:"Scan Brightness:"})," 128"]}),i.jsxs("div",{children:[i.jsx("strong",{children:"Auto-Zero Mode:"})," 1 (Use previous offset - recommended)"]}),i.jsxs("div",{children:[i.jsx("strong",{children:"Auto-Zero Frequency:"})," 127 (First cycle only - DFRobot recommended)"]}),i.jsxs("div",{children:[i.jsx("strong",{children:"Wait Time:"})," 0-10 (DFRobot default is 0, but low values for stability)"]})]}),i.jsx("p",{className:"text-xs text-slate-400 mt-2",children:"These settings provide optimal signal without saturation and are proven to work with Vivid White calibration."}),i.jsx(H,{onClick:()=>{l(150),o(2),c(128),y(1),w(127),S(5),n("Optimal settings applied! Remember to save settings.","success")},variant:"secondary",className:"text-xs px-3 py-1 mt-2",children:"Apply Optimal Settings"})]}),i.jsxs("div",{children:[i.jsx("label",{htmlFor:"autoZeroMode",className:"block text-sm font-medium text-slate-300 mb-1",children:"Auto-Zero Mode"}),i.jsxs("select",{id:"autoZeroMode",value:v.toString(),onChange:e=>y(parseInt(e.target.value,10)),className:"block w-full max-w-xs px-3 py-2 border border-slate-600 rounded-md bg-slate-700 text-slate-100 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",children:[i.jsx("option",{value:"0",children:"Always start at zero"}),i.jsx("option",{value:"1",children:"Use previous offset (recommended)"})]},`autoZeroMode-${v}`),i.jsx("p",{className:"text-xs text-slate-400 mt-1",children:"Mode 1 provides better stability by using previous calibration offset."})]}),i.jsx(X,{id:"autoZeroFreq",label:"Auto-Zero Frequency",min:0,max:255,value:x,onChange:w,unit:""}),i.jsx("p",{className:"text-xs text-slate-400 -mt-2",children:"0 = never, 127 = first cycle only (recommended), other values = every nth iteration"}),i.jsx(X,{id:"waitTime",label:"Wait Time",min:0,max:255,value:k,onChange:S,unit:""}),i.jsx("p",{className:"text-xs text-slate-400 -mt-2",children:"Wait time between measurements (0-255). Higher values improve stability but slow measurements."})]})}),i.jsx("div",{className:"border-t border-slate-600 pt-6",children:i.jsxs("div",{className:"space-y-4",children:[i.jsx("label",{className:"block text-sm font-medium text-slate-300",children:"White Point Calibration:"}),i.jsxs("div",{className:"bg-blue-900/30 p-3 rounded-lg border border-blue-600",children:[i.jsxs("p",{className:"text-sm text-blue-300 mb-3",children:["Calibrate white point using current sensor settings (ATIME: ",a,", AGAIN: ",null==(r=Q[s])?void 0:r.label,", Brightness: ",u,")."]}),i.jsx("div",{className:"grid grid-cols-1 gap-2",children:i.jsx(H,{onClick:async()=>{b(!0);try{const e=await A(u);e.success?(n("✅ White point calibration completed successfully!","success"),t({})):n(`❌ White point calibration failed: ${e.message}`,"error")}catch(e){n(`❌ White point calibration failed: ${e instanceof Error?e.message:"Unknown error"}`,"error")}finally{b(!1)}},variant:"primary",size:"sm",isLoading:g,className:"w-full",children:g?"Calibrating...":"Calibrate White Point"})}),i.jsx("p",{className:"text-xs text-slate-400 mt-2",children:"Place Dulux Vivid White paint sample under sensor. Calibration takes 50 readings over ~5 seconds for accuracy."})]}),i.jsx("label",{className:"block text-sm font-medium text-slate-300 mt-6",children:"Black Point Calibration:"}),i.jsxs("div",{className:"bg-gray-900/50 p-3 rounded-lg border border-gray-600",children:[i.jsx("p",{className:"text-sm text-gray-300 mb-3",children:"Calibrate black point for dark reference measurement. LEDs will remain OFF during calibration."}),i.jsx("div",{className:"grid grid-cols-1 gap-2",children:i.jsx(H,{onClick:async()=>{b(!0);try{const e=await async function(){return M(await fetch(`${O}/calibrate/black`,{method:"POST",headers:{"Content-Type":"application/json"}}))}();e.success?(n("✅ Black calibration completed successfully! LEDs were kept OFF during calibration.","success"),t({})):n(`❌ Black calibration failed: ${e.message}`,"error")}catch(e){n(`❌ Black calibration failed: ${e instanceof Error?e.message:"Unknown error"}`,"error")}finally{b(!1)}},variant:"secondary",size:"sm",isLoading:g,className:"w-full",children:g?"Calibrating...":"Calibrate Black Point"})}),i.jsx("p",{className:"text-xs text-slate-400 mt-2",children:"Cover sensor completely or place in dark environment. LEDs will be turned OFF automatically for dark reference measurement."})]})]})}),i.jsx("div",{className:"border-t border-slate-600 pt-6",children:i.jsxs("div",{className:"space-y-4",children:[i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsx("label",{className:"block text-sm font-medium text-slate-300",children:"🧪 Sensor Diagnostics & Testing"}),i.jsxs("div",{className:"flex gap-2",children:[i.jsx(H,{onClick:()=>window.open("./test_web_ui.html","_blank"),variant:"secondary",className:"text-xs px-3 py-1",title:"Open standalone test interface",children:"🔗 External Test"}),i.jsxs(H,{onClick:()=>j(!N),variant:"secondary",className:"text-xs px-3 py-1",children:[N?"Hide":"Show"," Tests"]})]})]}),N&&i.jsxs("div",{className:"space-y-4 bg-slate-800 p-4 rounded-lg border border-slate-600",children:[i.jsx("p",{className:"text-xs text-slate-400",children:"Comprehensive testing of TCS3430 advanced calibration settings. These tests validate parameter ranges, error handling, and sensor response."}),i.jsx("div",{className:"bg-slate-700 p-3 rounded border border-slate-600",children:i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsxs("div",{children:[i.jsx("p",{className:"text-xs font-medium text-slate-200",children:"🔗 Standalone Test Interface"}),i.jsx("p",{className:"text-xs text-slate-400",children:"Open external test page with manual IP configuration"})]}),i.jsx(H,{onClick:()=>window.open("./test_web_ui.html","_blank"),variant:"primary",className:"text-xs px-3 py-1",children:"Open Test Page"})]})}),i.jsxs("div",{className:"space-y-3",children:[i.jsx(H,{onClick:async()=>{if(!C){E(!0),_(!0),L("Current Settings Test",!0,"Testing current form values...");try{const e={autoZeroMode:v,autoZeroFreq:x,waitTime:k,atime:a,again:s,brightness:u};await R(e),await $(500);const t=await F();let r=!0;for(const[n,a]of Object.entries(e)){const e=t[n];e!==a&&(L("Current Settings Test",!1,`${n}: expected ${a}, got ${e}`),r=!1)}r?(L("Current Settings Test",!0,"All current settings applied successfully"),n("Current settings test passed!","success")):n("Some current settings failed to apply","error")}catch(e){const t=e instanceof Error?e.message:"Unknown error";L("Current Settings Test",!1,`Failed: ${t}`),n("Current settings test failed","error")}finally{E(!1)}}},disabled:C,variant:"primary",className:"w-full text-sm",isLoading:C,children:"🔧 Test Current Settings"}),i.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3",children:[i.jsx(H,{onClick:B,disabled:C,variant:"secondary",className:"text-sm",children:"Test Valid Values"}),i.jsx(H,{onClick:q,disabled:C,variant:"secondary",className:"text-sm",children:"Test Invalid Values"}),i.jsx(H,{onClick:W,disabled:C,variant:"secondary",className:"text-sm",children:"Test Combined Settings"}),i.jsx(H,{onClick:async()=>{if(!C){E(!0),_(!0),D(),L("Test Suite",!0,"🚀 Starting comprehensive calibration test suite...");try{const e=await Promise.all([B(),q(),W()]),t=e.filter((e=>e)).length,r=e.length;L("Test Suite Complete",t===r,`🎯 Results: ${t}/${r} test groups passed`),t===r?n("🎉 All calibration tests passed!","success"):n(`⚠️ ${r-t} test group(s) failed`,"error")}catch(e){L("Test Suite",!1,`Test suite failed: ${e}`),n("Test suite encountered an error","error")}finally{E(!1)}}},disabled:C,variant:"primary",className:"text-sm",isLoading:C,children:"Run Full Test Suite"})]})]}),i.jsxs("div",{className:"flex items-center justify-between",children:[i.jsxs(H,{onClick:()=>_(!z),variant:"secondary",className:"text-xs px-3 py-1",disabled:0===T.length,children:[z?"Hide":"Show"," Results (",T.length,")"]}),T.length>0&&i.jsx(H,{onClick:D,variant:"secondary",className:"text-xs px-3 py-1 text-red-400 hover:text-red-300",children:"Clear Results"})]}),z&&T.length>0&&i.jsx("div",{className:"bg-slate-900 p-3 rounded border border-slate-700 max-h-64 overflow-y-auto",children:i.jsx("div",{className:"space-y-1 font-mono text-xs",children:T.map(((e,t)=>i.jsxs("div",{className:"flex items-start space-x-2 "+(e.success?"text-green-400":"text-red-400"),children:[i.jsxs("span",{className:"text-slate-500 min-w-[60px]",children:["[",e.timestamp,"]"]}),i.jsx("span",{className:"min-w-[20px]",children:e.success?"✅":"❌"}),i.jsxs("span",{className:"font-medium min-w-[120px]",children:[e.test,":"]}),i.jsx("span",{className:"text-slate-300 break-all",children:e.message})]},t)))})})]})]})}),i.jsxs("div",{className:"mt-6 p-4 bg-blue-900/30 border border-blue-700 rounded-lg",children:[i.jsx("h3",{className:"text-lg font-semibold text-blue-400 mb-2",children:"🔧 Enhanced LED Control"}),i.jsx("p",{className:"text-slate-300",children:"Enhanced LED Control functionality has been integrated into the Live LED Control component in the sidebar for better user experience."})]}),i.jsxs("div",{className:"mt-6 p-4 bg-green-900/30 border border-green-700 rounded-lg",children:[i.jsx("h3",{className:"text-lg font-semibold text-green-400 mb-2",children:"📊 Live Sensor Metrics"}),i.jsx("p",{className:"text-slate-300",children:"Component placement test - this should be visible"}),i.jsx(ee,{isActive:!0,updateInterval:1500})]}),i.jsx("div",{className:"flex justify-end",children:i.jsx(H,{onClick:async()=>{h(!0);const e={atime:a,again:s,brightness:u,ledState:d,autoZeroMode:v,autoZeroFreq:x,waitTime:k},r=U(e);if(r.length>0)return n(`Invalid settings: ${r.join(", ")}`,"error"),void h(!1);try{await R(e),await new Promise((e=>setTimeout(e,750)));const r=await F(),i={atime:r.atime===a,again:r.again===s,brightness:r.brightness===u,autoZeroMode:r.autoZeroMode===v,autoZeroFreq:r.autoZeroFreq===x,waitTime:r.waitTime===k},d=Object.entries(i).filter((([e,t])=>!t)).map((([t,n])=>`${t} (expected: ${e[t]}, got: ${r[t]})`));0===d.length?(t(r),n("✅ All settings saved successfully!","success"),l(r.atime),o(r.again),c(r.brightness),y(r.autoZeroMode),w(r.autoZeroFreq),S(r.waitTime)):(n(`⚠️ Settings saved with changes: ${d.join(", ")}`,"error",8e3),l(r.atime),o(r.again),c(r.brightness),y(r.autoZeroMode),w(r.autoZeroFreq),S(r.waitTime),t(r))}catch(i){n(i instanceof Error?i.message:"Failed to save settings.","error")}finally{h(!1)}},isLoading:p,variant:"primary",children:"Save Settings"})})]})})},ne=({showToast:e,initialLedState:t})=>{var n;const[r,a]=f.useState(255),[l,s]=f.useState(255),[o,u]=f.useState(255),[c,d]=f.useState(128),[m,p]=f.useState(t||!1),[h,g]=f.useState(!1),[b,v]=f.useState(!1),[y,x]=f.useState(128),[w,k]=f.useState(!1),[S,N]=f.useState(null),[j,C]=f.useState(!1),[E,T]=f.useState(null),P=f.useRef(null),[L,O]=f.useState(128),[M,F]=f.useState(128),[R,A]=f.useState(0),[U,V]=f.useState([]);f.useEffect((()=>(B(),m&&W(),()=>Q())),[]),f.useEffect((()=>{m&&b?W():Q()}),[m,b]);const B=async()=>{try{const e=await fetch(`${z}${_}`,{method:"GET",headers:{Accept:"application/json"}});if(e.ok){const t=e.headers.get("content-type");if(t&&t.includes("application/json")){const t=await e.json();v(t.enhancedLEDMode??!1),x(t.manualLEDIntensity??128)}else k(!0)}}catch(e){k(!0)}},q=async()=>{try{const t={enhancedLEDMode:b,manualLEDIntensity:y},n=await fetch(`${z}${_}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(n.ok)e("Enhanced LED settings saved successfully!","success");else{const t=await n.text();e(`Failed to save settings: ${t}`,"error")}}catch(t){e(`Network error: ${t}`,"error")}},W=()=>{Q(),Z(),P.current=setInterval(Z,2e3)},Q=()=>{P.current&&(clearInterval(P.current),P.current=null)},Z=async()=>{try{const e=await fetch(`${z}/live-metrics`,{method:"GET",headers:{Accept:"application/json"}});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);{const t=e.headers.get("content-type");if(!t||!t.includes("application/json"))throw new Error("Live metrics endpoint not available - firmware update needed");{const t=await e.json();N(t),C(!0),T(null),b&&m&&t.sensorReadings&&Y(t)}}}catch(e){C(!1),T(e instanceof Error?e.message:"Unknown error")}},Y=async e=>{var t,n,r;const a=Date.now(),{x:l,y:s,z:i,ir:o}=e.sensorReadings,u=Math.max(l,s,i),c=(null==(t=e.metrics)?void 0:t.targetMin)||5e3,d=(null==(n=e.metrics)?void 0:n.targetMax)||6e4;let f=0;u<c?f=Math.min((c-u)/c,1):u>d&&(f=Math.min((u-d)/d,1));if(a-R<(f>.5?1e3:f>.2?2e3:f>0?3e3:5e3))return;const m=(null==(r=e.ledStatus)?void 0:r.currentBrightness)||L,p=u>0?o/u:0,h=p>.15;let g=m,b="";const v=.5+y/255*1.5,x=Math.round(40*(1+f)),w=Math.round(.05*(d-c)),k=c+w,S=d-w;if(u<k){const e=k-u,t=Math.min(Math.max(e/c,0),1),n=u<.5*c?1.5:1,r=Math.round(t*x*v*n),a=Math.max(2,Math.min(x,r));g=Math.min(255,m+a),b=`Low signal (${u} < ${k}), distance: ${e}, urgency: ${n.toFixed(1)}x, adj: +${a}`}else if(u>S){const e=u-S,t=Math.min(e/d,1),n=u>1.5*d?1.5:1,r=Math.round(t*x*v*n),a=Math.max(2,Math.min(x,r));g=Math.max(64,m-a),b=`High signal (${u} > ${S}), excess: ${e}, saturation: ${n.toFixed(1)}x, adj: -${a}`}else b=`Stable (${u} in range ${k}-${S})`;if(h&&g!==m){const e=Math.round(.3*Math.abs(g-m));g=Math.max(64,g-e),b+=` + IR compensation (${(100*p).toFixed(1)}%, -${e})`}const N=f>.3?.4*1.5:.4,j=N*g+(1-N)*M,C=Math.round(j);Math.abs(C-m)>1?(O(C),F(j),A(a),V((e=>[...e.slice(-9),C])),await G(C,b)):F(j)},G=async(e,t)=>{try{const t={brightness:e,r:r,g:l,b:o,keepOn:!0,enhancedMode:!0};(await D(t)).success}catch(n){}},J=async(t=!1)=>{g(!0),t&&b&&(v(!1),await q());const n=t?{brightness:0}:{brightness:b?L:c,r:r,g:l,b:o,keepOn:m,enhancedMode:b&&!t};try{const r=await D(n);r.success?(e(r.message||(t?"LED turned off.":"LED updated."),"success"),t?(p(!1),O(128),F(128),V([])):(p(!0),b&&(O(n.brightness),F(n.brightness)))):e(r.message||"Failed to update LED.","error")}catch(a){e(a instanceof Error?a.message:"Failed to update LED.","error")}finally{g(!1)}},ee=e=>{switch(e){case"optimal":case"normal":case"clean":case"adequate":return"text-green-400 bg-green-900/30";case"high":case"low":case"saturated":case"contaminated":return"text-red-400 bg-red-900/30";default:return"text-yellow-400 bg-yellow-900/30"}};return i.jsx(I,{title:"Live LED Control",className:"mb-6",children:i.jsxs("div",{className:"space-y-4",children:[w&&i.jsxs("div",{className:"mb-4 p-3 rounded-md text-sm bg-yellow-900/30 text-yellow-400 border border-yellow-700",children:[i.jsx("strong",{children:"Firmware Update Required:"})," Enhanced LED Control features require updated ESP32 firmware."]}),i.jsx("div",{className:"flex justify-center my-4",children:i.jsx($,{r:r,g:l,b:o,size:"lg"})}),i.jsxs("div",{className:"grid grid-cols-3 gap-2",children:[i.jsx(K,{id:"led-r",label:"R",type:"number",min:"0",max:"255",value:r,onChange:e=>a(Math.max(0,Math.min(255,parseInt(e.target.value)||0)))}),i.jsx(K,{id:"led-g",label:"G",type:"number",min:"0",max:"255",value:l,onChange:e=>s(Math.max(0,Math.min(255,parseInt(e.target.value)||0)))}),i.jsx(K,{id:"led-b",label:"B",type:"number",min:"0",max:"255",value:o,onChange:e=>u(Math.max(0,Math.min(255,parseInt(e.target.value)||0)))})]}),i.jsxs("div",{className:"flex items-center justify-between p-3 bg-slate-700/50 rounded-lg",children:[i.jsxs("div",{children:[i.jsx("label",{className:"text-sm font-medium text-slate-300",children:"Enhanced LED Control Mode"}),i.jsx("p",{className:"text-xs text-slate-500 mt-1",children:b?"Automatic brightness optimization for optimal sensor range (5,000-60,000)":"Manual LED intensity control"})]}),i.jsx("button",{onClick:async()=>{const e=!b;v(e),!e&&m&&(p(!1),await J(!0)),await q()},disabled:w,className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${b?"bg-blue-600":"bg-slate-600"} ${w?"opacity-50 cursor-not-allowed":""}`,children:i.jsx("span",{className:"inline-block h-4 w-4 transform rounded-full bg-white transition-transform "+(b?"translate-x-6":"translate-x-1")})})]}),i.jsx(X,{id:"live-brightness",label:b?"Optimization Sensitivity":"LED Brightness",min:b?50:0,max:255,value:b?y:c,onChange:b?x:d}),b&&i.jsxs("div",{className:"space-y-3 -mt-2",children:[i.jsxs("p",{className:"text-xs text-slate-500",children:["Sensitivity: ",y,"/255 - Higher values = more aggressive automatic adjustments"]}),i.jsxs("div",{className:"bg-gradient-to-r from-amber-900/20 to-orange-900/20 border border-amber-700/50 rounded-lg p-3",children:[i.jsxs("div",{className:"flex items-center justify-between mb-2",children:[i.jsx("h5",{className:"text-sm font-medium text-amber-300",children:"🔆 Real-time LED Brightness"}),i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx("div",{className:"w-2 h-2 rounded-full "+(m?"bg-green-400":"bg-red-400")}),i.jsx("span",{className:"text-xs text-slate-400",children:m?"Active":"Inactive"})]})]}),m?i.jsxs("div",{className:"space-y-3",children:[i.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[i.jsxs("div",{className:"bg-slate-800/50 rounded p-2",children:[i.jsx("div",{className:"text-amber-300 text-xs font-medium",children:"Current PWM"}),i.jsx("div",{className:"text-amber-100 font-mono text-xl",children:L}),i.jsxs("div",{className:"text-amber-400 text-xs",children:[(L/255*100).toFixed(1),"% duty cycle"]})]}),i.jsxs("div",{className:"bg-slate-800/50 rounded p-2",children:[i.jsx("div",{className:"text-amber-300 text-xs font-medium",children:"Smoothed Target"}),i.jsx("div",{className:"text-amber-100 font-mono text-xl",children:Math.round(M)}),i.jsxs("div",{className:"text-amber-400 text-xs",children:[(Math.round(M)/255*100).toFixed(1),"% target"]})]})]}),U.length>0&&i.jsxs("div",{className:"bg-slate-800/50 rounded p-2",children:[i.jsx("div",{className:"text-amber-300 text-xs font-medium",children:"PWM History (Recent Adjustments)"}),i.jsx("div",{className:"text-amber-100 font-mono text-sm",children:U.slice(-5).join(" → ")}),i.jsxs("div",{className:"text-amber-400 text-xs mt-1",children:["Range: ",Math.min(...U.slice(-5))," - ",Math.max(...U.slice(-5)),"(",Math.max(...U.slice(-5))-Math.min(...U.slice(-5))," point spread)"]})]})]}):i.jsxs("div",{className:"text-center py-4",children:[i.jsx("div",{className:"text-slate-400 text-sm",children:"LED is OFF"}),i.jsx("div",{className:"text-slate-500 text-xs",children:'Click "Start Enhanced LED" to begin real-time monitoring'})]})]}),m&&i.jsx("div",{className:"text-xs text-slate-400 bg-slate-700/30 rounded p-2",children:i.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{children:"Auto Brightness:"}),i.jsxs("span",{className:"font-mono text-slate-200",children:[L,"/255"]})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{children:"EMA Smoothed:"}),i.jsxs("span",{className:"font-mono text-slate-200",children:[Math.round(M),"/255"]})]})]})})]}),b&&m&&i.jsxs("div",{className:"p-3 bg-slate-700/30 rounded-lg border border-slate-600",children:[i.jsxs("div",{className:"flex items-center justify-between mb-3",children:[i.jsx("h4",{className:"text-sm font-medium text-slate-300",children:"Live Sensor Feedback"}),i.jsxs("div",{className:"flex items-center space-x-2",children:[i.jsx("div",{className:"w-2 h-2 rounded-full "+(j?"bg-green-400":"bg-red-400")}),i.jsx("span",{className:"text-xs text-slate-400",children:j?"Connected":"Disconnected"})]})]}),E&&i.jsx("div",{className:"mb-3 p-2 rounded text-xs bg-red-900/30 text-red-400 border border-red-700",children:E}),S&&i.jsxs("div",{className:"space-y-3",children:[i.jsxs("div",{children:[i.jsx("h5",{className:"text-xs font-medium text-slate-300 mb-2",children:"TCS3430 Raw Channel Values"}),i.jsxs("div",{className:"grid grid-cols-3 gap-2 text-sm",children:[i.jsxs("div",{className:"bg-red-900/20 border border-red-700/50 rounded p-2",children:[i.jsx("div",{className:"text-red-300 text-xs font-medium",children:"R (Red)"}),i.jsx("div",{className:"text-red-100 font-mono text-sm",children:S.sensorReadings.x})]}),i.jsxs("div",{className:"bg-green-900/20 border border-green-700/50 rounded p-2",children:[i.jsx("div",{className:"text-green-300 text-xs font-medium",children:"G (Green)"}),i.jsx("div",{className:"text-green-100 font-mono text-sm",children:S.sensorReadings.y})]}),i.jsxs("div",{className:"bg-blue-900/20 border border-blue-700/50 rounded p-2",children:[i.jsx("div",{className:"text-blue-300 text-xs font-medium",children:"B (Blue)"}),i.jsx("div",{className:"text-blue-100 font-mono text-sm",children:S.sensorReadings.z})]}),i.jsxs("div",{className:"bg-slate-800/50 border border-slate-600 rounded p-2",children:[i.jsx("div",{className:"text-slate-300 text-xs font-medium",children:"C (Clear)"}),i.jsx("div",{className:"text-slate-100 font-mono text-sm",children:S.sensorReadings.x+S.sensorReadings.y+S.sensorReadings.z})]}),i.jsxs("div",{className:"bg-purple-900/20 border border-purple-700/50 rounded p-2",children:[i.jsx("div",{className:"text-purple-300 text-xs font-medium",children:"IR"}),i.jsx("div",{className:"text-purple-100 font-mono text-sm",children:S.sensorReadings.ir})]}),i.jsxs("div",{className:"bg-slate-800/50 border border-slate-600 rounded p-2",children:[i.jsx("div",{className:"text-slate-300 text-xs font-medium",children:"Status"}),i.jsxs("div",{className:"text-slate-100 font-mono text-sm",children:["0x",null==(n=S.sensorReadings.status)?void 0:n.toString(16).toUpperCase().padStart(2,"0")]})]})]})]}),i.jsxs("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[i.jsxs("div",{className:"bg-slate-800/50 rounded p-2",children:[i.jsx("span",{className:"text-slate-400",children:"Current Brightness:"}),i.jsx("span",{className:"text-slate-200 ml-2 font-mono",children:S.ledStatus.currentBrightness})]}),i.jsxs("div",{className:"bg-slate-800/50 rounded p-2",children:[i.jsx("span",{className:"text-slate-400",children:"Control Variable:"}),i.jsxs("div",{className:"flex items-center space-x-1 mt-1",children:[i.jsx("span",{className:"text-slate-200 font-mono text-xs",children:S.metrics.controlVariable}),i.jsx("span",{className:`px-1 py-0.5 rounded text-xs ${ee(S.statusIndicators.controlVariableStatus)}`,children:S.statusIndicators.controlVariableStatus})]})]})]}),i.jsxs("div",{className:"text-xs",children:[i.jsxs("div",{className:"flex justify-between items-center",children:[i.jsx("span",{className:"text-slate-400",children:"Target Range:"}),i.jsxs("span",{className:"text-slate-300",children:[S.metrics.targetMin," - ",S.metrics.targetMax]})]}),i.jsxs("div",{className:"flex justify-between items-center mt-1",children:[i.jsx("span",{className:"text-slate-400",children:"In Optimal Range:"}),i.jsx("span",{className:"px-2 py-0.5 rounded text-xs "+(S.metrics.inOptimalRange?"text-green-400 bg-green-900/30":"text-yellow-400 bg-yellow-900/30"),children:S.metrics.inOptimalRange?"Yes":"No"})]}),i.jsxs("div",{className:"flex justify-between items-center mt-1",children:[i.jsx("span",{className:"text-slate-400",children:"Auto-Optimization:"}),i.jsx("span",{className:"px-2 py-0.5 rounded text-xs "+(b&&m?"text-blue-400 bg-blue-900/30":"text-slate-400 bg-slate-700/30"),children:b&&m?"Active":"Inactive"})]}),b&&m&&i.jsxs("div",{className:"flex justify-between items-center mt-1",children:[i.jsx("span",{className:"text-slate-400",children:"IR Ratio:"}),i.jsxs("span",{className:"px-1 py-0.5 rounded text-xs "+(S.metrics.irRatio>.15?"text-red-400 bg-red-900/30":"text-green-400 bg-green-900/30"),children:[(100*S.metrics.irRatio).toFixed(1),"%"]})]})]}),i.jsxs("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"Saturation:"}),i.jsx("span",{className:`px-1 py-0.5 rounded ${ee(S.statusIndicators.saturationStatus)}`,children:S.statusIndicators.saturationStatus})]}),i.jsxs("div",{className:"flex justify-between",children:[i.jsx("span",{className:"text-slate-400",children:"IR Status:"}),i.jsx("span",{className:`px-1 py-0.5 rounded ${ee(S.statusIndicators.irContaminationStatus)}`,children:S.statusIndicators.irContaminationStatus})]})]})]})]}),i.jsxs("div",{className:"flex space-x-2 mt-4",children:[i.jsx(H,{onClick:()=>J(!1),isLoading:h,variant:"primary",className:"flex-1",disabled:b&&!m,children:b?"Start Enhanced LED":"Set LED"}),i.jsx(H,{onClick:()=>J(!0),isLoading:h,variant:"secondary",className:"flex-1",children:"Turn LED Off"})]}),b&&i.jsxs("div",{className:"p-3 bg-blue-900/20 rounded-lg border border-blue-700/50",children:[i.jsx("h4",{className:"text-sm font-medium text-blue-300 mb-2",children:"Enhanced Mode Features"}),i.jsxs("ul",{className:"text-xs text-blue-200 space-y-1",children:[i.jsx("li",{children:"• Automatic brightness optimization using max(R,G,B) control variable"}),i.jsx("li",{children:"• Target range: 5,000 - 60,000 for optimal sensor readings"}),i.jsx("li",{children:"• IR contamination detection and compensation (threshold: 15%)"}),i.jsx("li",{children:"• Exponential moving average smoothing to prevent flickering"}),i.jsx("li",{children:"• Sensitivity-based adjustment scaling (0.5x - 2.0x multiplier)"}),i.jsx("li",{children:"• 3-second throttling between automatic adjustments"})]}),m&&i.jsxs("div",{className:"mt-3 pt-2 border-t border-blue-700/30",children:[i.jsxs("p",{className:"text-xs text-blue-300",children:[i.jsx("strong",{children:"Status:"})," Auto-optimization is ",b&&m?"ACTIVE":"INACTIVE"]}),i.jsx("p",{className:"text-xs text-blue-400 mt-1",children:"Adjust sensitivity slider to control how aggressively the system optimizes brightness."})]})]})]})})},re=()=>{const[e,t]=f.useState(null),[n,r]=f.useState([]),[a,l]=f.useState(!0),[s,o]=f.useState(!0),[u,c]=f.useState(null),[d,m]=f.useState(null),[p,h]=f.useState(null),g=(e,t,n)=>{h({message:e,type:t,id:Date.now()});const r=e.includes("Calibration values too high (saturated)");setTimeout((()=>h(null)),n||(r?13e3:3e3))},b=f.useCallback((async()=>{l(!0),c(null);try{const e=await F();t(e)}catch(e){c(e instanceof Error?e.message:"Failed to load device status."),g(e instanceof Error?e.message:"Failed to load device status.","error")}finally{l(!1)}}),[]),v=f.useCallback((async()=>{o(!0),m(null);try{const e=(await async function(){return M(await fetch(`${O}/samples`))}()).samples.sort(((e,t)=>t.timestamp-e.timestamp));r(e)}catch(e){m(e instanceof Error?e.message:"Failed to load samples."),g(e instanceof Error?e.message:"Failed to load samples.","error")}finally{o(!1)}}),[]);f.useEffect((()=>{b(),v();const e=setInterval(b,3e4);return()=>clearInterval(e)}),[]);const y=()=>{v(),b()};return i.jsxs("div",{className:"min-h-screen bg-slate-900 p-4 md:p-8",children:[p&&i.jsx("div",{className:`fixed top-5 right-5 p-4 rounded-md shadow-lg text-white ${p.message.includes("Calibration values too high (saturated)")?"animate-fadeInOutBackLong":"animate-fadeInOutBack"} ${"success"===p.type?"bg-green-600":"bg-red-600"}`,style:{zIndex:1e3},children:p.message}),i.jsx("header",{className:"mb-8",children:i.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[i.jsxs("div",{className:"text-center sm:text-left",children:[i.jsx("h1",{className:"text-4xl font-bold text-slate-100",children:"ESP32 Color Matcher"}),i.jsx("p",{className:"text-slate-400",children:"Web Interface"})]}),i.jsx("nav",{className:"mt-4 sm:mt-0 flex justify-center sm:justify-end",children:i.jsxs("a",{href:`${z}${L}`,target:"_blank",rel:"noopener noreferrer","aria-label":"Open device settings (external link)",className:"flex items-center gap-1.5 px-3 py-2 sm:px-4 sm:py-2 rounded-md text-sm font-medium text-amber-700 bg-amber-100 hover:bg-amber-200 hover:shadow-md transition-all duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-amber-400 border border-amber-300",children:[i.jsx("span",{className:"hidden sm:inline",children:"Device Settings"}),i.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5",children:[i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.646.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 0 1 0 1.905c-.007.379.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 0 1-.22.128c-.333.184-.582.496-.646.87l-.212 1.282c-.09.542-.56.94-1.11.94h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.759 6.759 0 0 1 0-1.905c.007-.379-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.184.582-.496.644-.87l.214-1.282Z"}),i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"})]})]})})]})}),i.jsxs("main",{className:"max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-6",children:[i.jsxs("section",{className:"lg:col-span-2 space-y-6",children:[i.jsx(B,{status:e,isLoading:a,error:u,onRefresh:b}),i.jsx(q,{onSampleSaved:y,showToast:g}),i.jsx(Z,{}),i.jsx(G,{samples:n,isLoading:s,error:d,onSampleDeleted:y,showToast:g})]}),i.jsxs("aside",{className:"lg:col-span-1 space-y-6",children:[e&&i.jsx(ne,{showToast:g,initialLedState:null==e?void 0:e.ledState}),i.jsx(te,{initialSettings:e,onSettingsChange:e=>{t((t=>t?{...t,...e}:null))},showToast:g})]})]}),i.jsx("footer",{className:"mt-12 text-center text-sm text-slate-400",children:i.jsxs("p",{children:["© ",(new Date).getFullYear()," ESP32 Color Matcher Interface. For device firmware version X.Y.Z."]})}),i.jsx("style",{children:"\n        @keyframes fadeInOutBack {\n          0% { opacity: 0; transform: translateY(-20px); }\n          10%, 90% { opacity: 1; transform: translateY(0); }\n          100% { opacity: 0; transform: translateY(-20px); }\n        }\n        @keyframes fadeInOutBackLong {\n          0% { opacity: 0; transform: translateY(-20px); }\n          5%, 95% { opacity: 1; transform: translateY(0); }\n          100% { opacity: 0; transform: translateY(-20px); }\n        }\n        .animate-fadeInOutBack {\n          animation: fadeInOutBack 3s ease-in-out forwards;\n        }\n        .animate-fadeInOutBackLong {\n          animation: fadeInOutBackLong 13s ease-in-out forwards;\n        }\n      "})]})},ae=document.getElementById("root");if(!ae)throw new Error("Could not find root element to mount to");P.createRoot(ae).render(i.jsx(m.StrictMode,{children:i.jsx(re,{})}));
